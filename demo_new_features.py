#!/usr/bin/env python3
"""
新功能演示腳本：檢測框視覺優化和檢測間隔控制
"""

import cv2
import numpy as np
import argparse
import logging
import time
from datetime import datetime

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_demo_frame(frame_num, width=800, height=600):
    """創建演示幀"""
    # 創建背景
    frame = np.zeros((height, width, 3), dtype=np.uint8)
    frame[:] = (50, 50, 50)  # 深灰色背景
    
    # 添加移動的"人頭"
    num_heads = 4
    colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 255, 255)]  # 與ROI顏色一致
    
    for i in range(num_heads):
        # 計算移動軌跡
        x = int(150 + 500 * (i + 1) / (num_heads + 1) + 100 * np.sin(frame_num * 0.05 + i))
        y = int(150 + 200 * np.sin(frame_num * 0.03 + i * 2))
        
        # 確保在畫面內
        x = max(50, min(width - 50, x))
        y = max(50, min(height - 50, y))
        
        # 繪製"頭部"（模擬檢測框）
        color = colors[i % len(colors)]
        
        # 頭部檢測框（最細線條 thickness=1）
        cv2.rectangle(frame, (x-25, y-25), (x+25, y+25), color, 1)
        
        # ID標籤
        label = f'{i+1}'
        cv2.rectangle(frame, (x-25, y-35), (x-10, y-25), color, -1)
        cv2.putText(frame, label, (x-22, y-28), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        
        # 信心值
        conf = 0.85 + 0.1 * np.sin(frame_num * 0.1 + i)
        cv2.putText(frame, f'{conf:.2f}', (x-22, y-40), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        # 中心點
        cv2.circle(frame, (x, y), 3, color, -1)
    
    return frame

def draw_roi_regions(frame):
    """繪製ROI區域"""
    colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 255, 255)]
    
    # 定義ROI區域
    roi_regions = [
        {'name': 'Region_1', 'points': [[100, 100], [300, 100], [300, 250], [100, 250]], 'color': colors[0]},
        {'name': 'Region_2', 'points': [[350, 120], [550, 120], [550, 280], [350, 280]], 'color': colors[1]},
        {'name': 'Region_3', 'points': [[600, 100], [750, 100], [750, 300], [600, 300]], 'color': colors[2]},
        {'name': 'Region_4', 'points': [[200, 350], [600, 350], [600, 500], [200, 500]], 'color': colors[3]},
    ]
    
    for region in roi_regions:
        color = region['color']
        roi_np = np.array([region['points']], dtype=np.int32)
        cv2.polylines(frame, roi_np, isClosed=True, color=color, thickness=2)
        
        # 區域標籤
        label_pos = tuple(region['points'][0])
        cv2.putText(frame, region['name'], 
                   (label_pos[0], label_pos[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
    
    return roi_regions

def add_detection_info(frame, frame_num, detection_interval, should_detect):
    """添加檢測信息"""
    height, width = frame.shape[:2]
    
    # 總計數
    total_count = 4
    cv2.putText(frame, f'Total Count: {total_count}', (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
    
    # 各區域計數
    region_counts = [1, 1, 1, 1]
    y_offset = 70
    for i, count in enumerate(region_counts):
        cv2.putText(frame, f'Region_{i+1}: {count}', (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
        y_offset += 25
    
    # 檢測狀態
    status = "DETECT" if should_detect else "CACHED"
    detection_time = 25.5 if should_detect else 0.0
    fps = 30.0
    
    fps_text = f'FPS: {fps:.1f} | Det: {detection_time:.1f}ms | {status}'
    cv2.putText(frame, fps_text, (10, 180), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
    
    # 檢測間隔信息
    interval_text = f'Detection Interval: {detection_interval} frames'
    cv2.putText(frame, interval_text, (10, 210), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
    
    # 模式信息
    mode_text = 'Mode: PERSON (GPU) - Multi-Region'
    cv2.putText(frame, mode_text, (10, 240), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
    
    # GPU信息
    gpu_text = 'GPU Memory: 1024MB'
    cv2.putText(frame, gpu_text, (10, 270), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # 時間戳
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    cv2.putText(frame, timestamp, (10, height - 10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 幀號
    cv2.putText(frame, f'Frame: {frame_num}', (width - 150, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

def run_demo(detection_interval=5, duration=30):
    """運行演示"""
    logger.info(f"開始新功能演示 - 檢測間隔: {detection_interval} 幀")
    
    frame_num = 0
    detection_count = 0
    start_time = time.time()
    
    cv2.namedWindow('New Features Demo', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('New Features Demo', 800, 600)
    
    try:
        while True:
            frame_num += 1
            
            # 檢測間隔控制
            should_detect = (frame_num % detection_interval == 1)
            if should_detect:
                detection_count += 1
            
            # 創建演示幀
            frame = create_demo_frame(frame_num)
            
            # 繪製ROI區域
            roi_regions = draw_roi_regions(frame)
            
            # 添加檢測信息
            add_detection_info(frame, frame_num, detection_interval, should_detect)
            
            # 添加說明文字
            help_text = [
                "New Features Demo:",
                "1. Detection boxes use thickness=1 (thinnest)",
                "2. Box colors match ROI region colors", 
                "3. Detection interval control for performance",
                f"4. Current interval: every {detection_interval} frames",
                "",
                "Press 'q' to quit, '1'-'9' to change interval"
            ]
            
            for i, text in enumerate(help_text):
                cv2.putText(frame, text, (400, 350 + i * 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # 顯示幀
            cv2.imshow('New Features Demo', frame)
            
            # 處理鍵盤輸入
            key = cv2.waitKey(33) & 0xFF  # ~30 FPS
            if key == ord('q'):
                break
            elif key >= ord('1') and key <= ord('9'):
                new_interval = key - ord('0')
                detection_interval = new_interval
                logger.info(f"檢測間隔已更改為: {detection_interval} 幀")
            
            # 統計信息
            if frame_num % 100 == 0:
                elapsed = time.time() - start_time
                fps = frame_num / elapsed
                efficiency = (detection_count / frame_num) * 100
                logger.info(f"幀 {frame_num}: FPS {fps:.1f}, 檢測效率 {efficiency:.1f}% ({detection_count}/{frame_num})")
            
            # 時間限制
            if time.time() - start_time > duration:
                logger.info(f"演示時間結束 ({duration} 秒)")
                break
                
    except KeyboardInterrupt:
        logger.info("用戶中斷演示")
    finally:
        cv2.destroyAllWindows()
        
        # 最終統計
        elapsed = time.time() - start_time
        fps = frame_num / elapsed if elapsed > 0 else 0
        efficiency = (detection_count / frame_num) * 100 if frame_num > 0 else 0
        
        logger.info("=== 演示統計 ===")
        logger.info(f"總幀數: {frame_num}")
        logger.info(f"檢測次數: {detection_count}")
        logger.info(f"檢測效率: {efficiency:.1f}%")
        logger.info(f"平均FPS: {fps:.1f}")
        logger.info(f"檢測間隔: {detection_interval} 幀")

def main():
    parser = argparse.ArgumentParser(description='新功能演示')
    parser.add_argument('--detection-interval', type=int, default=5,
                       help='檢測間隔（幀數），預設為5')
    parser.add_argument('--duration', type=int, default=30,
                       help='演示持續時間（秒），預設為30')
    
    args = parser.parse_args()
    
    # 參數驗證
    if args.detection_interval < 1:
        logger.warning("檢測間隔不能小於1，已重設為1")
        args.detection_interval = 1
    elif args.detection_interval > 30:
        logger.warning("檢測間隔過大，已限制為30幀")
        args.detection_interval = 30
    
    logger.info("=== 新功能演示 ===")
    logger.info("功能1: 檢測框線條粗細優化為1（最細）")
    logger.info("功能2: 檢測框顏色與ROI區域顏色一致")
    logger.info("功能3: 檢測間隔控制提升性能")
    logger.info("")
    logger.info("控制說明:")
    logger.info("- 按 'q' 退出演示")
    logger.info("- 按 '1'-'9' 更改檢測間隔")
    logger.info("")
    
    run_demo(args.detection_interval, args.duration)
    
    return 0

if __name__ == "__main__":
    exit(main())
