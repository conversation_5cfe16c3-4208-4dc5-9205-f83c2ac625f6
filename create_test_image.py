#!/usr/bin/env python3
"""
建立測試圖片
用於測試頭部檢測 GUI 功能
"""

import cv2
import numpy as np
import os

def create_test_image():
    """建立包含人形輪廓的測試圖片"""
    # 建立空白圖片
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 240  # 淺灰色背景
    
    # 繪製多個人形輪廓
    people_positions = [
        (150, 200),  # 左側人物
        (400, 180),  # 中間人物
        (650, 220),  # 右側人物
    ]
    
    for i, (x, y) in enumerate(people_positions):
        # 繪製頭部（圓形）
        head_radius = 30
        cv2.circle(image, (x, y), head_radius, (200, 150, 100), -1)  # 膚色
        cv2.circle(image, (x, y), head_radius, (150, 100, 50), 2)   # 輪廓
        
        # 繪製眼睛
        cv2.circle(image, (x-10, y-5), 3, (0, 0, 0), -1)
        cv2.circle(image, (x+10, y-5), 3, (0, 0, 0), -1)
        
        # 繪製嘴巴
        cv2.ellipse(image, (x, y+10), (8, 4), 0, 0, 180, (0, 0, 0), 1)
        
        # 繪製身體（矩形）
        body_width, body_height = 40, 80
        body_top = y + head_radius
        cv2.rectangle(image, 
                     (x - body_width//2, body_top),
                     (x + body_width//2, body_top + body_height),
                     (100, 100, 200), -1)  # 藍色衣服
        cv2.rectangle(image, 
                     (x - body_width//2, body_top),
                     (x + body_width//2, body_top + body_height),
                     (50, 50, 150), 2)     # 輪廓
        
        # 繪製手臂
        arm_length = 50
        cv2.line(image, (x - body_width//2, body_top + 20), 
                (x - body_width//2 - arm_length, body_top + 40), (200, 150, 100), 8)
        cv2.line(image, (x + body_width//2, body_top + 20), 
                (x + body_width//2 + arm_length, body_top + 40), (200, 150, 100), 8)
        
        # 繪製腿部
        leg_length = 60
        cv2.line(image, (x - 10, body_top + body_height), 
                (x - 10, body_top + body_height + leg_length), (50, 50, 50), 8)
        cv2.line(image, (x + 10, body_top + body_height), 
                (x + 10, body_top + body_height + leg_length), (50, 50, 50), 8)
        
        # 添加標籤
        cv2.putText(image, f'Person {i+1}', (x-30, y-50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    # 添加標題
    cv2.putText(image, 'Head Detection Test Image', (250, 50), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # 添加說明文字
    cv2.putText(image, 'This image contains 3 people for testing head detection', 
               (150, height - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (100, 100, 100), 1)
    
    return image

def create_complex_test_image():
    """建立更複雜的測試圖片"""
    height, width = 800, 1000
    image = np.ones((height, width, 3), dtype=np.uint8) * 250
    
    # 建立群體場景
    people_grid = [
        (150, 200), (250, 180), (350, 220), (450, 190), (550, 210),
        (200, 350), (300, 330), (400, 370), (500, 340),
        (250, 500), (350, 480), (450, 520),
    ]
    
    for i, (x, y) in enumerate(people_grid):
        # 隨機大小的頭部
        head_size = np.random.randint(25, 35)
        
        # 頭部
        cv2.circle(image, (x, y), head_size, (200, 150, 100), -1)
        cv2.circle(image, (x, y), head_size, (150, 100, 50), 1)
        
        # 簡化的身體
        body_height = head_size * 2
        cv2.rectangle(image, 
                     (x - head_size//2, y + head_size),
                     (x + head_size//2, y + head_size + body_height),
                     (100, 100, 200), -1)
        
        # 編號
        cv2.putText(image, str(i+1), (x-5, y+5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # 標題
    cv2.putText(image, 'Complex Head Detection Test - Multiple People', 
               (200, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # 統計資訊
    cv2.putText(image, f'Total People: {len(people_grid)}', 
               (50, height - 50), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    return image

def main():
    """主函數"""
    print("建立測試圖片...")
    
    # 建立測試圖片目錄
    test_dir = "test_images"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 建立簡單測試圖片
    simple_image = create_test_image()
    simple_path = os.path.join(test_dir, "simple_test.jpg")
    cv2.imwrite(simple_path, simple_image)
    print(f"✅ 簡單測試圖片已儲存: {simple_path}")
    
    # 建立複雜測試圖片
    complex_image = create_complex_test_image()
    complex_path = os.path.join(test_dir, "complex_test.jpg")
    cv2.imwrite(complex_path, complex_image)
    print(f"✅ 複雜測試圖片已儲存: {complex_path}")
    
    # 建立空白測試圖片（用於測試無檢測結果的情況）
    blank_image = np.ones((400, 600, 3), dtype=np.uint8) * 200
    cv2.putText(blank_image, 'No People - Empty Test Image', 
               (100, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (100, 100, 100), 2)
    blank_path = os.path.join(test_dir, "blank_test.jpg")
    cv2.imwrite(blank_path, blank_image)
    print(f"✅ 空白測試圖片已儲存: {blank_path}")
    
    print(f"\n測試圖片已建立在 {test_dir} 目錄中")
    print("可以在 GUI 中載入這些圖片來測試檢測功能")

if __name__ == "__main__":
    main()
