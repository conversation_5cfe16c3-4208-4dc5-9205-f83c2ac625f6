# 頭部檢測系統 GUI 版本

基於 `gpu_optimized_head_detection.py` 的圖形化使用者介面，提供直觀易用的頭部檢測功能。

## 功能特色

### 🎯 核心功能
- **圖片檢測**：支援 JPG, PNG, BMP, TIFF, WebP 等格式
- **影片檢測**：支援 MP4, AVI, MOV, MKV, WMV, FLV 等格式
- **即時顯示**：原始圖片與檢測結果對比顯示
- **GPU 加速**：支援 CUDA GPU 加速運算

### 📊 統計分析
- **即時統計**：檢測數量、平均值、最大值等
- **趨勢圖表**：檢測數量變化趨勢
- **區域分佈**：多區域檢測結果分析
- **數據匯出**：統計報告和圖表匯出

### ⚙️ 參數控制
- **信心度調整**：可調整檢測敏感度 (0.1-0.9)
- **模型選擇**：支援 person 和 custom 模型
- **設備選擇**：GPU 或 CPU 運算模式
- **顯示選項**：檢測框和標籤顯示控制

### 💾 結果儲存
- **檢測結果**：JSON 格式統計數據
- **統計圖表**：PNG/PDF 格式圖表匯出
- **標註圖片**：帶檢測框的結果圖片

## 安裝需求

### 系統需求
- Python 3.7 或更高版本
- Windows/Linux/macOS
- 建議 8GB 以上記憶體
- 可選：NVIDIA GPU + CUDA（用於 GPU 加速）

### 必要套件
```bash
pip install torch torchvision opencv-python Pillow matplotlib numpy ultralytics
```

或使用提供的需求檔案：
```bash
pip install -r requirements_gui.txt
```

## 快速開始

### 方法一：使用啟動腳本（推薦）
```bash
python run_gui.py
```
啟動腳本會自動檢查環境並安裝缺少的套件。

### 方法二：直接啟動
```bash
python head_detection_gui.py
```

## 使用說明

### 1. 載入檔案
- 點擊「選擇圖片」載入圖片檔案
- 點擊「選擇影片」載入影片檔案
- 支援拖拽操作（部分系統）

### 2. 調整參數
- **信心度閾值**：拖動滑桿調整檢測敏感度
- **模型類型**：選擇 person（通用）或 custom（專用）
- **運算設備**：選擇 CUDA（GPU）或 CPU

### 3. 開始檢測
- 點擊「開始檢測」執行檢測
- 檢測過程中可點擊「停止檢測」中斷
- 結果會即時顯示在右側面板

### 4. 查看結果
- **原始圖片**：顯示未處理的原始圖片
- **檢測結果**：顯示帶檢測框的結果圖片
- **統計資訊**：右側面板顯示詳細統計
- **圖表分析**：趨勢圖和分佈圖

### 5. 儲存結果
- **儲存結果**：匯出 JSON 格式統計數據
- **匯出統計**：匯出 PNG/PDF 格式圖表
- **重設統計**：清除所有統計數據

## 介面說明

### 主視窗佈局
```
┌─────────────────────────────────────────────────────────┐
│ 檔案  檢測  說明                                        │
├─────────────┬─────────────────────────┬─────────────────┤
│             │                         │                 │
│  控制面板   │      顯示區域           │   統計面板      │
│             │                         │                 │
│ • 檔案選擇  │  ┌─原始圖片─┐          │ • 即時統計      │
│ • 檢測參數  │  └─檢測結果─┘          │ • 統計圖表      │
│ • 顯示選項  │                         │                 │
│ • 操作控制  │                         │                 │
│             │                         │                 │
├─────────────┴─────────────────────────┴─────────────────┤
│ 狀態列：就緒                    檢測資訊：檢測到 X 個頭部 │
└─────────────────────────────────────────────────────────┘
```

### 控制面板功能
- **檔案選擇**：圖片/影片載入按鈕
- **檢測參數**：信心度、模型、設備選擇
- **顯示選項**：檢測框、標籤顯示控制
- **操作控制**：開始/停止檢測、儲存結果

### 統計面板功能
- **即時統計**：當前檢測數據摘要
- **統計圖表**：
  - 檢測數量趨勢圖
  - 區域分佈圓餅圖

## 進階功能

### 批次處理
- 支援影片逐幀檢測
- 自動統計整個影片的檢測結果
- 可設定檢測間隔以提高效率

### 多區域檢測
- 繼承原始程式的多區域 ROI 功能
- 支援不同區域的獨立統計
- 區域檢測結果視覺化

### 效能優化
- GPU 記憶體管理
- 批次處理優化
- 多線程處理避免介面凍結

## 故障排除

### 常見問題

**Q: 程式無法啟動**
A: 檢查 Python 版本和必要套件是否已安裝

**Q: GPU 模式不可用**
A: 確認已安裝 CUDA 和對應版本的 PyTorch

**Q: 檢測速度很慢**
A: 嘗試降低圖片解析度或使用 GPU 模式

**Q: 記憶體不足**
A: 關閉其他程式或使用較小的批次大小

### 錯誤訊息
- `檢測器初始化失敗`：檢查模型檔案和網路連接
- `無法載入圖片/影片`：確認檔案格式和路徑正確
- `CUDA 不可用`：檢查 GPU 驅動和 CUDA 安裝

## 技術架構

### 核心組件
- **檢測引擎**：基於 YOLOv5 的 GPUOptimizedHeadDetector
- **GUI 框架**：Tkinter（Python 內建）
- **圖表繪製**：Matplotlib
- **影像處理**：OpenCV + PIL

### 檔案結構
```
├── head_detection_gui.py          # 主 GUI 程式
├── gpu_optimized_head_detection.py # 檢測核心
├── run_gui.py                     # 啟動腳本
├── requirements_gui.txt           # 套件需求
└── README_GUI.md                  # 說明文件
```

## 版本資訊

- **版本**：1.0
- **更新日期**：2024年
- **相容性**：Python 3.7+
- **授權**：請參考原始專案授權

## 支援與回饋

如有問題或建議，請：
1. 檢查本說明文件的故障排除章節
2. 確認環境配置正確
3. 查看程式日誌輸出
4. 提供詳細的錯誤訊息和系統資訊
