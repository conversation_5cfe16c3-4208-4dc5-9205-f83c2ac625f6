#!/usr/bin/env python3
"""
RTSP GUI 功能測試腳本
測試 GUI 中的 RTSP 串流功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import cv2
import numpy as np

class RTSPTestGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("RTSP 功能測試")
        self.root.geometry("800x600")
        
        self.is_testing = False
        self.test_thread = None
        
        self.create_gui()
    
    def create_gui(self):
        """建立測試 GUI"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 標題
        title_label = ttk.Label(main_frame, text="RTSP 串流功能測試", font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # RTSP URL 輸入
        url_frame = ttk.LabelFrame(main_frame, text="RTSP 設定", padding=10)
        url_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(url_frame, text="RTSP URL:").pack(anchor=tk.W)
        self.rtsp_url = tk.StringVar(value='rtsp://root:Abc_123@111.70.10.205:7040/axis-media/media.amp?resolution=1280x800')
        url_entry = ttk.Entry(url_frame, textvariable=self.rtsp_url, width=80)
        url_entry.pack(fill=tk.X, pady=5)
        
        # 測試 URL 選項
        test_urls = [
            "rtsp://admin:admin@192.168.1.100:554/stream",
            "rtsp://test:<EMAIL>/live",
            "rtsp://user:pass@10.0.0.1:8554/video"
        ]
        
        ttk.Label(url_frame, text="測試 URL 範例:").pack(anchor=tk.W, pady=(10, 5))
        for i, url in enumerate(test_urls):
            ttk.Button(url_frame, text=f"範例 {i+1}: {url[:50]}...", 
                      command=lambda u=url: self.rtsp_url.set(u)).pack(fill=tk.X, pady=2)
        
        # 控制按鈕
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=10)
        
        self.test_button = ttk.Button(control_frame, text="測試 RTSP 連接", command=self.test_rtsp_connection)
        self.test_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.gui_button = ttk.Button(control_frame, text="啟動完整 GUI", command=self.launch_full_gui)
        self.gui_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="關閉", command=self.root.quit).pack(side=tk.RIGHT)
        
        # 結果顯示
        result_frame = ttk.LabelFrame(main_frame, text="測試結果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, height=15)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始說明
        self.log_message("RTSP 功能測試工具")
        self.log_message("=" * 50)
        self.log_message("1. 輸入或選擇 RTSP URL")
        self.log_message("2. 點擊「測試 RTSP 連接」驗證連接")
        self.log_message("3. 點擊「啟動完整 GUI」開始使用")
        self.log_message("")
    
    def log_message(self, message):
        """記錄訊息到結果區域"""
        timestamp = time.strftime("%H:%M:%S")
        self.result_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.result_text.see(tk.END)
        self.root.update_idletasks()
    
    def test_rtsp_connection(self):
        """測試 RTSP 連接"""
        if self.is_testing:
            self.log_message("測試正在進行中...")
            return
        
        rtsp_url = self.rtsp_url.get().strip()
        if not rtsp_url:
            messagebox.showwarning("警告", "請輸入 RTSP URL")
            return
        
        self.is_testing = True
        self.test_button.config(state=tk.DISABLED, text="測試中...")
        
        # 在新線程中執行測試
        self.test_thread = threading.Thread(target=self.rtsp_test_worker, args=(rtsp_url,))
        self.test_thread.daemon = True
        self.test_thread.start()
    
    def rtsp_test_worker(self, rtsp_url):
        """RTSP 測試工作線程"""
        try:
            self.log_message(f"開始測試 RTSP 連接...")
            self.log_message(f"URL: {rtsp_url}")
            
            # 測試基本連接
            self.log_message("正在嘗試連接...")
            cap = cv2.VideoCapture(rtsp_url)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 10000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 10000)
            
            if not cap.isOpened():
                self.log_message("❌ 無法連接到 RTSP 串流")
                self.log_message("可能的原因:")
                self.log_message("  - URL 格式錯誤")
                self.log_message("  - 網路連接問題")
                self.log_message("  - 認證資訊錯誤")
                self.log_message("  - 串流服務不可用")
                return
            
            self.log_message("✅ RTSP 連接成功!")
            
            # 獲取串流資訊
            fps = int(cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            self.log_message(f"串流資訊:")
            self.log_message(f"  - 解析度: {width}x{height}")
            self.log_message(f"  - 幀率: {fps} fps")
            
            # 測試讀取幀
            self.log_message("正在測試幀讀取...")
            frame_count = 0
            success_count = 0
            
            for i in range(10):  # 測試讀取10幀
                ret, frame = cap.read()
                frame_count += 1
                
                if ret and frame is not None:
                    success_count += 1
                    if i == 0:  # 第一幀
                        self.log_message(f"✅ 成功讀取第一幀: {frame.shape}")
                else:
                    self.log_message(f"⚠️  第 {i+1} 幀讀取失敗")
                
                time.sleep(0.1)  # 短暫延遲
            
            success_rate = (success_count / frame_count) * 100
            self.log_message(f"幀讀取測試完成:")
            self.log_message(f"  - 總幀數: {frame_count}")
            self.log_message(f"  - 成功幀數: {success_count}")
            self.log_message(f"  - 成功率: {success_rate:.1f}%")
            
            if success_rate >= 80:
                self.log_message("✅ RTSP 串流品質良好，可以正常使用")
            elif success_rate >= 50:
                self.log_message("⚠️  RTSP 串流品質一般，可能會有延遲")
            else:
                self.log_message("❌ RTSP 串流品質較差，建議檢查網路連接")
            
            cap.release()
            
            # 測試檢測器整合
            self.log_message("")
            self.log_message("測試檢測器整合...")
            try:
                from gpu_optimized_head_detection import GPUOptimizedHeadDetector, GPUOptimizedRTSPProcessor
                self.log_message("✅ 檢測器模組導入成功")
                
                # 不實際初始化檢測器，避免下載模型
                self.log_message("✅ RTSP 處理器可用")
                self.log_message("✅ 所有組件準備就緒")
                
            except Exception as e:
                self.log_message(f"❌ 檢測器整合測試失敗: {e}")
            
            self.log_message("")
            self.log_message("🎉 RTSP 測試完成！可以啟動完整 GUI 進行檢測。")
            
        except Exception as e:
            self.log_message(f"❌ RTSP 測試失敗: {e}")
            self.log_message("請檢查 URL 格式和網路連接")
            
        finally:
            self.is_testing = False
            self.root.after(0, lambda: self.test_button.config(state=tk.NORMAL, text="測試 RTSP 連接"))
    
    def launch_full_gui(self):
        """啟動完整的 GUI"""
        try:
            self.log_message("正在啟動完整 GUI...")
            
            # 啟動主 GUI
            import subprocess
            import sys
            
            # 嘗試啟動 GUI
            result = subprocess.Popen([sys.executable, "head_detection_gui.py"], 
                                    cwd=".", 
                                    stdout=subprocess.PIPE, 
                                    stderr=subprocess.PIPE)
            
            self.log_message("✅ GUI 已啟動")
            self.log_message("請在主 GUI 中輸入 RTSP URL 並連接")
            
        except Exception as e:
            self.log_message(f"❌ 啟動 GUI 失敗: {e}")
            messagebox.showerror("錯誤", f"無法啟動 GUI:\n{str(e)}")

def main():
    """主程式"""
    root = tk.Tk()
    app = RTSPTestGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("程式被中斷")

if __name__ == "__main__":
    main()
