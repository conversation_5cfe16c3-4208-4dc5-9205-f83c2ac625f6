#!/usr/bin/env python3
"""
頭部檢測 GUI 應用程式
基於 gpu_optimized_head_detection.py 的圖形化使用者介面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import queue
import logging

# 導入原始的檢測器類別
from gpu_optimized_head_detection import GPUOptimizedHeadDetector

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HeadDetectionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("頭部檢測系統 - GPU 優化版本")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # 初始化變數
        self.detector = None
        self.current_image = None
        self.current_video = None
        self.rtsp_processor = None
        self.detection_results = []
        self.statistics = {
            'total_detections': 0,
            'detection_history': [],
            'region_stats': {},
            'session_start': datetime.now()
        }

        # RTSP 相關變數
        self.rtsp_url = tk.StringVar(value='rtsp://root:Abc_123@111.70.10.205:7040/axis-media/media.amp?resolution=1280x800')
        self.is_rtsp_streaming = False
        self.rtsp_thread = None
        self.roi_regions = []  # 多區域 ROI
        self.current_roi_points = []  # 當前繪製的 ROI 點
        self.roi_mode = False  # ROI 編輯模式
        
        # GUI 控制變數
        self.conf_threshold = tk.DoubleVar(value=0.4)
        self.show_boxes = tk.BooleanVar(value=True)
        self.show_labels = tk.BooleanVar(value=True)
        self.model_type = tk.StringVar(value='person')
        self.device_type = tk.StringVar(value='cuda')
        
        # 處理狀態
        self.is_processing = False
        self.processing_thread = None
        self.result_queue = queue.Queue()
        
        # 建立 GUI 介面
        self.create_gui()
        
        # 初始化檢測器
        self.initialize_detector()
        
        # 啟動結果處理循環
        self.process_results()
    
    def create_gui(self):
        """建立主要 GUI 介面"""
        # 建立主選單
        self.create_menu()
        
        # 建立主要框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左側控制面板
        self.create_control_panel(main_frame)
        
        # 中央顯示區域
        self.create_display_area(main_frame)
        
        # 右側統計面板
        self.create_statistics_panel(main_frame)
        
        # 底部狀態列
        self.create_status_bar()
    
    def create_menu(self):
        """建立選單列"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 檔案選單
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="檔案", menu=file_menu)
        file_menu.add_command(label="開啟圖片", command=self.open_image)
        file_menu.add_command(label="開啟影片", command=self.open_video)
        file_menu.add_separator()
        file_menu.add_command(label="儲存結果", command=self.save_results)
        file_menu.add_command(label="匯出統計", command=self.export_statistics)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 檢測選單
        detect_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="檢測", menu=detect_menu)
        detect_menu.add_command(label="開始檢測", command=self.start_detection)
        detect_menu.add_command(label="停止檢測", command=self.stop_detection)
        detect_menu.add_separator()
        detect_menu.add_command(label="重設統計", command=self.reset_statistics)
        
        # 說明選單
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="說明", menu=help_menu)
        help_menu.add_command(label="使用說明", command=self.show_help)
        help_menu.add_command(label="關於", command=self.show_about)
    
    def create_control_panel(self, parent):
        """建立左側控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding=10)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        
        # 檔案選擇區域
        file_frame = ttk.LabelFrame(control_frame, text="輸入來源", padding=5)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(file_frame, text="選擇圖片", command=self.open_image).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="選擇影片", command=self.open_video).pack(fill=tk.X, pady=2)

        # RTSP 串流設定
        ttk.Label(file_frame, text="RTSP 串流地址:").pack(anchor=tk.W, pady=(10, 0))
        rtsp_entry = ttk.Entry(file_frame, textvariable=self.rtsp_url, font=('Consolas', 9))
        rtsp_entry.pack(fill=tk.X, pady=2)

        rtsp_button_frame = ttk.Frame(file_frame)
        rtsp_button_frame.pack(fill=tk.X, pady=2)

        self.rtsp_connect_button = ttk.Button(rtsp_button_frame, text="連接 RTSP", command=self.connect_rtsp)
        self.rtsp_connect_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))

        self.rtsp_disconnect_button = ttk.Button(rtsp_button_frame, text="斷開連接", command=self.disconnect_rtsp, state=tk.DISABLED)
        self.rtsp_disconnect_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))
        
        # 檢測參數設定
        param_frame = ttk.LabelFrame(control_frame, text="檢測參數", padding=5)
        param_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 信心度閾值
        ttk.Label(param_frame, text="信心度閾值:").pack(anchor=tk.W)
        conf_scale = ttk.Scale(param_frame, from_=0.1, to=0.9, variable=self.conf_threshold, 
                              orient=tk.HORIZONTAL, command=self.update_confidence)
        conf_scale.pack(fill=tk.X, pady=2)
        self.conf_label = ttk.Label(param_frame, text=f"當前值: {self.conf_threshold.get():.2f}")
        self.conf_label.pack(anchor=tk.W)
        
        # 模型選擇
        ttk.Label(param_frame, text="模型類型:").pack(anchor=tk.W, pady=(10, 0))
        model_combo = ttk.Combobox(param_frame, textvariable=self.model_type, 
                                  values=['person', 'custom'], state='readonly')
        model_combo.pack(fill=tk.X, pady=2)
        model_combo.bind('<<ComboboxSelected>>', self.on_model_change)
        
        # 設備選擇
        ttk.Label(param_frame, text="運算設備:").pack(anchor=tk.W, pady=(10, 0))
        device_combo = ttk.Combobox(param_frame, textvariable=self.device_type,
                                   values=['cuda', 'cpu'], state='readonly')
        device_combo.pack(fill=tk.X, pady=2)
        device_combo.bind('<<ComboboxSelected>>', self.on_device_change)
        
        # 顯示選項
        display_frame = ttk.LabelFrame(control_frame, text="顯示選項", padding=5)
        display_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Checkbutton(display_frame, text="顯示檢測框", variable=self.show_boxes).pack(anchor=tk.W)
        ttk.Checkbutton(display_frame, text="顯示標籤", variable=self.show_labels).pack(anchor=tk.W)

        # ROI 區域設定
        roi_frame = ttk.LabelFrame(control_frame, text="ROI 區域設定", padding=5)
        roi_frame.pack(fill=tk.X, pady=(0, 10))

        roi_button_frame1 = ttk.Frame(roi_frame)
        roi_button_frame1.pack(fill=tk.X, pady=2)

        self.roi_mode_button = ttk.Button(roi_button_frame1, text="開始繪製 ROI", command=self.toggle_roi_mode)
        self.roi_mode_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))

        ttk.Button(roi_button_frame1, text="清除 ROI", command=self.clear_roi).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))

        roi_button_frame2 = ttk.Frame(roi_frame)
        roi_button_frame2.pack(fill=tk.X, pady=2)

        ttk.Button(roi_button_frame2, text="載入 ROI", command=self.load_roi).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        ttk.Button(roi_button_frame2, text="儲存 ROI", command=self.save_roi).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))

        # ROI 狀態顯示
        self.roi_status_label = ttk.Label(roi_frame, text="ROI 狀態: 無區域", font=('Arial', 8))
        self.roi_status_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 控制按鈕
        button_frame = ttk.LabelFrame(control_frame, text="操作控制", padding=5)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.detect_button = ttk.Button(button_frame, text="開始檢測", command=self.start_detection)
        self.detect_button.pack(fill=tk.X, pady=2)
        
        self.stop_button = ttk.Button(button_frame, text="停止檢測", command=self.stop_detection, state=tk.DISABLED)
        self.stop_button.pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="儲存結果", command=self.save_results).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="重設統計", command=self.reset_statistics).pack(fill=tk.X, pady=2)
    
    def create_display_area(self, parent):
        """建立中央顯示區域"""
        display_frame = ttk.Frame(parent)
        display_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # 圖片顯示區域
        image_frame = ttk.LabelFrame(display_frame, text="檢測結果顯示", padding=5)
        image_frame.pack(fill=tk.BOTH, expand=True)
        
        # 建立 Notebook 用於切換顯示
        self.notebook = ttk.Notebook(image_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 原始圖片標籤頁
        self.original_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.original_frame, text="原始圖片")
        
        self.original_canvas = tk.Canvas(self.original_frame, bg='gray90')
        self.original_canvas.pack(fill=tk.BOTH, expand=True)

        # 檢測結果標籤頁
        self.result_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.result_frame, text="檢測結果")

        self.result_canvas = tk.Canvas(self.result_frame, bg='gray90')
        self.result_canvas.pack(fill=tk.BOTH, expand=True)

        # 綁定鼠標事件用於 ROI 繪製
        self.result_canvas.bind("<Button-1>", self.on_canvas_click)
        self.result_canvas.bind("<Button-3>", self.on_canvas_right_click)
        self.result_canvas.bind("<Motion>", self.on_canvas_motion)
        
        # 進度條
        self.progress = ttk.Progressbar(display_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(5, 0))
    
    def create_statistics_panel(self, parent):
        """建立右側統計面板"""
        stats_frame = ttk.LabelFrame(parent, text="統計資訊", padding=10)
        stats_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # 即時統計
        current_stats_frame = ttk.LabelFrame(stats_frame, text="當前統計", padding=5)
        current_stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.stats_text = tk.Text(current_stats_frame, height=8, width=25, wrap=tk.WORD)
        stats_scrollbar = ttk.Scrollbar(current_stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 統計圖表
        chart_frame = ttk.LabelFrame(stats_frame, text="統計圖表", padding=5)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # 建立 matplotlib 圖表
        self.fig = Figure(figsize=(4, 6), dpi=80)
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 初始化圖表
        self.init_charts()
    
    def create_status_bar(self):
        """建立狀態列"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="就緒")
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 檢測資訊標籤
        self.detection_info = ttk.Label(self.status_bar, text="")
        self.detection_info.pack(side=tk.RIGHT, padx=5, pady=2)
    
    def initialize_detector(self):
        """初始化檢測器"""
        try:
            self.detector = GPUOptimizedHeadDetector(
                model_type=self.model_type.get(),
                conf_threshold=self.conf_threshold.get(),
                device=self.device_type.get()
            )
            self.update_status("檢測器初始化完成")
            logger.info("檢測器初始化成功")
        except Exception as e:
            self.update_status(f"檢測器初始化失敗: {str(e)}")
            messagebox.showerror("錯誤", f"檢測器初始化失敗:\n{str(e)}")
            logger.error(f"檢測器初始化失敗: {e}")

    def update_status(self, message):
        """更新狀態列"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def update_confidence(self, value):
        """更新信心度閾值"""
        conf_value = float(value)
        self.conf_label.config(text=f"當前值: {conf_value:.2f}")
        if self.detector:
            self.detector.conf_threshold = conf_value
            self.detector.model.conf = conf_value

    def on_model_change(self, event=None):
        """模型類型改變時的處理"""
        self.update_status("重新初始化檢測器...")
        self.initialize_detector()

    def on_device_change(self, event=None):
        """設備類型改變時的處理"""
        self.update_status("重新初始化檢測器...")
        self.initialize_detector()

    def open_image(self):
        """開啟圖片檔案"""
        file_types = [
            ('圖片檔案', '*.jpg *.jpeg *.png *.bmp *.tiff *.webp'),
            ('JPEG', '*.jpg *.jpeg'),
            ('PNG', '*.png'),
            ('所有檔案', '*.*')
        ]

        filename = filedialog.askopenfilename(
            title="選擇圖片檔案",
            filetypes=file_types
        )

        if filename:
            try:
                self.current_image = cv2.imread(filename)
                if self.current_image is None:
                    raise ValueError("無法讀取圖片檔案")

                self.current_video = None  # 清除影片
                self.display_original_image()
                self.update_status(f"已載入圖片: {os.path.basename(filename)}")
                logger.info(f"載入圖片: {filename}")

            except Exception as e:
                messagebox.showerror("錯誤", f"無法載入圖片:\n{str(e)}")
                logger.error(f"載入圖片失敗: {e}")

    def open_video(self):
        """開啟影片檔案"""
        file_types = [
            ('影片檔案', '*.mp4 *.avi *.mov *.mkv *.wmv *.flv'),
            ('MP4', '*.mp4'),
            ('AVI', '*.avi'),
            ('所有檔案', '*.*')
        ]

        filename = filedialog.askopenfilename(
            title="選擇影片檔案",
            filetypes=file_types
        )

        if filename:
            try:
                self.current_video = cv2.VideoCapture(filename)
                if not self.current_video.isOpened():
                    raise ValueError("無法開啟影片檔案")

                self.current_image = None  # 清除圖片

                # 讀取第一幀作為預覽
                ret, frame = self.current_video.read()
                if ret:
                    self.current_image = frame
                    self.display_original_image()

                self.current_video.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重設到開始
                self.update_status(f"已載入影片: {os.path.basename(filename)}")
                logger.info(f"載入影片: {filename}")

            except Exception as e:
                messagebox.showerror("錯誤", f"無法載入影片:\n{str(e)}")
                logger.error(f"載入影片失敗: {e}")

    def display_original_image(self):
        """顯示原始圖片"""
        if self.current_image is not None:
            # 調整圖片大小以適應畫布
            display_image = self.resize_image_for_display(self.current_image)

            # 轉換為 PIL 格式
            image_rgb = cv2.cvtColor(display_image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            photo = ImageTk.PhotoImage(pil_image)

            # 清除畫布並顯示圖片
            self.original_canvas.delete("all")
            canvas_width = self.original_canvas.winfo_width()
            canvas_height = self.original_canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                x = (canvas_width - photo.width()) // 2
                y = (canvas_height - photo.height()) // 2
                self.original_canvas.create_image(x, y, anchor=tk.NW, image=photo)
                self.original_canvas.image = photo  # 保持引用

    def resize_image_for_display(self, image, max_width=600, max_height=400):
        """調整圖片大小以適應顯示"""
        height, width = image.shape[:2]

        # 計算縮放比例
        scale_w = max_width / width
        scale_h = max_height / height
        scale = min(scale_w, scale_h, 1.0)  # 不放大圖片

        if scale < 1.0:
            new_width = int(width * scale)
            new_height = int(height * scale)
            return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

        return image

    def start_detection(self):
        """開始檢測"""
        if self.current_image is None and self.current_video is None and not self.is_rtsp_streaming:
            messagebox.showwarning("警告", "請先選擇圖片、影片檔案或連接 RTSP 串流")
            return

        if self.detector is None:
            messagebox.showerror("錯誤", "檢測器未初始化")
            return

        if self.is_processing:
            messagebox.showinfo("提示", "檢測正在進行中")
            return

        # 如果是 RTSP 串流，檢查是否已經在運行
        if self.is_rtsp_streaming:
            if self.rtsp_thread and self.rtsp_thread.is_alive():
                messagebox.showinfo("提示", "RTSP 檢測已在運行中")
                return
            else:
                messagebox.showinfo("提示", "RTSP 串流已連接，檢測正在進行中")
                return

        self.is_processing = True
        self.detect_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress.start()

        # 在新線程中執行檢測
        self.processing_thread = threading.Thread(target=self.detection_worker)
        self.processing_thread.daemon = True
        self.processing_thread.start()

        self.update_status("檢測進行中...")

    def stop_detection(self):
        """停止檢測"""
        self.is_processing = False
        self.detect_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress.stop()
        self.update_status("檢測已停止")

    def detection_worker(self):
        """檢測工作線程"""
        try:
            if self.current_video is not None:
                self.process_video()
            else:
                self.process_image()
        except Exception as e:
            self.result_queue.put(('error', str(e)))
        finally:
            self.result_queue.put(('finished', None))

    def process_image(self):
        """處理單張圖片"""
        try:
            # 執行檢測
            results = self.detector.detect_heads_batch([self.current_image])
            result = results[0]

            # 提取檢測結果
            total_count = result['total_count']
            annotated_frame = result['annotated_frame']

            # 更新統計
            self.update_detection_statistics(total_count, result['region_results'])

            # 將結果放入隊列
            self.result_queue.put(('image_result', {
                'count': total_count,
                'annotated_image': annotated_frame,
                'original_image': self.current_image
            }))

        except Exception as e:
            logger.error(f"圖片檢測失敗: {e}")
            self.result_queue.put(('error', f"圖片檢測失敗: {str(e)}"))

    def process_video(self):
        """處理影片"""
        try:
            frame_count = 0
            detection_results = []

            while self.is_processing:
                ret, frame = self.current_video.read()
                if not ret:
                    break

                frame_count += 1

                # 每隔幾幀檢測一次以提高效率
                if frame_count % 5 == 1:
                    results = self.detector.detect_heads_batch([frame])
                    result = results[0]

                    total_count = result['total_count']
                    annotated_frame = result['annotated_frame']

                    # 更新統計
                    self.update_detection_statistics(total_count, result['region_results'])
                    detection_results.append({
                        'frame': frame_count,
                        'count': total_count,
                        'timestamp': datetime.now()
                    })

                    # 將結果放入隊列
                    self.result_queue.put(('video_frame', {
                        'frame_count': frame_count,
                        'count': total_count,
                        'annotated_image': annotated_frame,
                        'original_image': frame
                    }))

                # 控制播放速度
                time.sleep(0.03)  # 約30fps

            # 影片處理完成
            self.result_queue.put(('video_complete', {
                'total_frames': frame_count,
                'results': detection_results
            }))

        except Exception as e:
            logger.error(f"影片檢測失敗: {e}")
            self.result_queue.put(('error', f"影片檢測失敗: {str(e)}"))

    def update_detection_statistics(self, count, region_results):
        """更新檢測統計"""
        self.statistics['total_detections'] += count
        self.statistics['detection_history'].append({
            'timestamp': datetime.now(),
            'count': count,
            'regions': {r['region_name']: r['count'] for r in region_results}
        })

        # 保持歷史記錄在合理範圍內
        if len(self.statistics['detection_history']) > 100:
            self.statistics['detection_history'] = self.statistics['detection_history'][-100:]

        # 更新區域統計
        for region_result in region_results:
            region_name = region_result['region_name']
            if region_name not in self.statistics['region_stats']:
                self.statistics['region_stats'][region_name] = {
                    'total': 0,
                    'max': 0,
                    'count': 0
                }

            stats = self.statistics['region_stats'][region_name]
            stats['total'] += region_result['count']
            stats['max'] = max(stats['max'], region_result['count'])
            stats['count'] += 1

    def process_results(self):
        """處理檢測結果隊列"""
        try:
            while True:
                try:
                    result_type, data = self.result_queue.get_nowait()

                    if result_type == 'image_result':
                        self.display_detection_result(data)
                        self.update_statistics_display()
                        self.update_charts()

                    elif result_type == 'video_frame':
                        self.display_detection_result(data)
                        self.update_statistics_display()
                        if data['frame_count'] % 10 == 0:  # 每10幀更新一次圖表
                            self.update_charts()

                    elif result_type == 'rtsp_frame':
                        self.display_detection_result(data)
                        self.update_statistics_display()
                        if data['frame_count'] % 20 == 0:  # 每20幀更新一次圖表
                            self.update_charts()

                    elif result_type == 'video_complete':
                        self.update_status(f"影片處理完成，共處理 {data['total_frames']} 幀")
                        self.update_charts()

                    elif result_type == 'rtsp_disconnected':
                        self.disconnect_rtsp()
                        self.update_status("RTSP 串流意外斷開")

                    elif result_type == 'error':
                        messagebox.showerror("檢測錯誤", data)
                        self.update_status("檢測發生錯誤")

                    elif result_type == 'finished':
                        self.stop_detection()

                except queue.Empty:
                    break

        except Exception as e:
            logger.error(f"處理結果時發生錯誤: {e}")

        # 每100ms檢查一次結果隊列
        self.root.after(100, self.process_results)

    def display_detection_result(self, data):
        """顯示檢測結果"""
        try:
            annotated_image = data['annotated_image']
            count = data['count']

            # 保存最後的標註幀用於 ROI 繪製
            self.last_annotated_frame = annotated_image.copy()

            # 如果有 ROI 區域，繪製在圖片上
            if self.roi_regions or self.current_roi_points:
                annotated_image = self.draw_roi_on_frame(annotated_image)

            # 調整圖片大小
            display_image = self.resize_image_for_display(annotated_image)

            # 轉換為 PIL 格式
            image_rgb = cv2.cvtColor(display_image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            photo = ImageTk.PhotoImage(pil_image)

            # 顯示在結果畫布上
            self.result_canvas.delete("all")
            canvas_width = self.result_canvas.winfo_width()
            canvas_height = self.result_canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                x = (canvas_width - photo.width()) // 2
                y = (canvas_height - photo.height()) // 2
                self.result_canvas.create_image(x, y, anchor=tk.NW, image=photo)
                self.result_canvas.image = photo  # 保持引用

            # 切換到結果標籤頁
            self.notebook.select(self.result_frame)

            # 更新檢測資訊
            self.detection_info.config(text=f"檢測到 {count} 個頭部")

        except Exception as e:
            logger.error(f"顯示檢測結果失敗: {e}")

    def update_statistics_display(self):
        """更新統計顯示"""
        try:
            stats_text = f"=== 檢測統計 ===\n"
            stats_text += f"總檢測數量: {self.statistics['total_detections']}\n"
            stats_text += f"檢測次數: {len(self.statistics['detection_history'])}\n"

            if self.statistics['detection_history']:
                recent_counts = [h['count'] for h in self.statistics['detection_history'][-10:]]
                avg_count = sum(recent_counts) / len(recent_counts)
                max_count = max(recent_counts)
                stats_text += f"近期平均: {avg_count:.1f}\n"
                stats_text += f"近期最大: {max_count}\n"

            stats_text += f"\n=== 區域統計 ===\n"
            for region_name, stats in self.statistics['region_stats'].items():
                if stats['count'] > 0:
                    avg = stats['total'] / stats['count']
                    stats_text += f"{region_name}:\n"
                    stats_text += f"  平均: {avg:.1f}\n"
                    stats_text += f"  最大: {stats['max']}\n"
                    stats_text += f"  次數: {stats['count']}\n"

            # 更新文字框
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)

        except Exception as e:
            logger.error(f"更新統計顯示失敗: {e}")

    def init_charts(self):
        """初始化統計圖表"""
        try:
            # 清除現有圖表
            self.fig.clear()

            # 建立子圖
            self.ax1 = self.fig.add_subplot(2, 1, 1)
            self.ax2 = self.fig.add_subplot(2, 1, 2)

            # 設置圖表標題
            self.ax1.set_title('檢測數量趨勢', fontsize=10)
            self.ax2.set_title('區域分佈', fontsize=10)

            # 設置中文字體
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False

            self.fig.tight_layout()
            self.canvas.draw()

        except Exception as e:
            logger.error(f"初始化圖表失敗: {e}")

    def update_charts(self):
        """更新統計圖表"""
        try:
            # 清除現有圖表
            self.ax1.clear()
            self.ax2.clear()

            # 檢測數量趨勢圖
            if self.statistics['detection_history']:
                history = self.statistics['detection_history'][-20:]  # 最近20次
                counts = [h['count'] for h in history]
                times = list(range(len(counts)))

                self.ax1.plot(times, counts, 'b-o', markersize=4, linewidth=2)
                self.ax1.set_title('檢測數量趨勢 (最近20次)', fontsize=10)
                self.ax1.set_xlabel('檢測次數')
                self.ax1.set_ylabel('檢測數量')
                self.ax1.grid(True, alpha=0.3)

                # 添加平均線
                if len(counts) > 1:
                    avg_count = sum(counts) / len(counts)
                    self.ax1.axhline(y=avg_count, color='r', linestyle='--', alpha=0.7, label=f'平均: {avg_count:.1f}')
                    self.ax1.legend(fontsize=8)

            # 區域分佈圓餅圖
            if self.statistics['region_stats']:
                region_names = []
                region_totals = []

                for name, stats in self.statistics['region_stats'].items():
                    if stats['total'] > 0:
                        region_names.append(name)
                        region_totals.append(stats['total'])

                if region_totals:
                    colors = plt.cm.Set3(np.linspace(0, 1, len(region_names)))
                    wedges, texts, autotexts = self.ax2.pie(region_totals, labels=region_names,
                                                           autopct='%1.1f%%', colors=colors, startangle=90)

                    # 調整文字大小
                    for text in texts:
                        text.set_fontsize(8)
                    for autotext in autotexts:
                        autotext.set_fontsize(7)
                        autotext.set_color('white')
                        autotext.set_weight('bold')

                    self.ax2.set_title('區域檢測分佈', fontsize=10)

            self.fig.tight_layout()
            self.canvas.draw()

        except Exception as e:
            logger.error(f"更新圖表失敗: {e}")

    def save_results(self):
        """儲存檢測結果"""
        if not self.statistics['detection_history']:
            messagebox.showwarning("警告", "沒有檢測結果可儲存")
            return

        try:
            # 選擇儲存位置
            filename = filedialog.asksaveasfilename(
                title="儲存檢測結果",
                defaultextension=".json",
                filetypes=[
                    ('JSON檔案', '*.json'),
                    ('所有檔案', '*.*')
                ]
            )

            if filename:
                # 準備儲存數據
                save_data = {
                    'session_info': {
                        'start_time': self.statistics['session_start'].isoformat(),
                        'end_time': datetime.now().isoformat(),
                        'model_type': self.model_type.get(),
                        'device': self.device_type.get(),
                        'confidence_threshold': self.conf_threshold.get()
                    },
                    'statistics': {
                        'total_detections': self.statistics['total_detections'],
                        'detection_count': len(self.statistics['detection_history']),
                        'region_stats': self.statistics['region_stats']
                    },
                    'detection_history': [
                        {
                            'timestamp': h['timestamp'].isoformat(),
                            'count': h['count'],
                            'regions': h['regions']
                        } for h in self.statistics['detection_history']
                    ]
                }

                # 儲存到檔案
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"檢測結果已儲存到:\n{filename}")
                self.update_status(f"結果已儲存: {os.path.basename(filename)}")

        except Exception as e:
            messagebox.showerror("錯誤", f"儲存失敗:\n{str(e)}")
            logger.error(f"儲存結果失敗: {e}")

    def export_statistics(self):
        """匯出統計圖表"""
        if not self.statistics['detection_history']:
            messagebox.showwarning("警告", "沒有統計數據可匯出")
            return

        try:
            # 選擇儲存位置
            filename = filedialog.asksaveasfilename(
                title="匯出統計圖表",
                defaultextension=".png",
                filetypes=[
                    ('PNG圖片', '*.png'),
                    ('PDF檔案', '*.pdf'),
                    ('所有檔案', '*.*')
                ]
            )

            if filename:
                # 建立新的圖表用於匯出
                export_fig = plt.figure(figsize=(12, 8))

                # 檢測數量趨勢
                ax1 = export_fig.add_subplot(2, 2, 1)
                history = self.statistics['detection_history']
                counts = [h['count'] for h in history]
                times = list(range(len(counts)))

                ax1.plot(times, counts, 'b-o', markersize=3)
                ax1.set_title('檢測數量趨勢')
                ax1.set_xlabel('檢測次數')
                ax1.set_ylabel('檢測數量')
                ax1.grid(True, alpha=0.3)

                # 區域分佈
                if self.statistics['region_stats']:
                    ax2 = export_fig.add_subplot(2, 2, 2)
                    region_names = []
                    region_totals = []

                    for name, stats in self.statistics['region_stats'].items():
                        if stats['total'] > 0:
                            region_names.append(name)
                            region_totals.append(stats['total'])

                    if region_totals:
                        ax2.pie(region_totals, labels=region_names, autopct='%1.1f%%', startangle=90)
                        ax2.set_title('區域檢測分佈')

                # 時間分佈直方圖
                ax3 = export_fig.add_subplot(2, 2, 3)
                ax3.hist(counts, bins=min(10, len(set(counts))), alpha=0.7, color='green')
                ax3.set_title('檢測數量分佈')
                ax3.set_xlabel('檢測數量')
                ax3.set_ylabel('頻率')

                # 統計摘要文字
                ax4 = export_fig.add_subplot(2, 2, 4)
                ax4.axis('off')

                summary_text = f"""統計摘要
總檢測數量: {self.statistics['total_detections']}
檢測次數: {len(self.statistics['detection_history'])}
平均每次: {self.statistics['total_detections']/len(self.statistics['detection_history']):.1f}
最大檢測: {max(counts) if counts else 0}
最小檢測: {min(counts) if counts else 0}

會話時間: {self.statistics['session_start'].strftime('%Y-%m-%d %H:%M:%S')}
匯出時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, fontsize=10,
                        verticalalignment='top', fontfamily='monospace')

                export_fig.tight_layout()
                export_fig.savefig(filename, dpi=300, bbox_inches='tight')
                plt.close(export_fig)

                messagebox.showinfo("成功", f"統計圖表已匯出到:\n{filename}")
                self.update_status(f"圖表已匯出: {os.path.basename(filename)}")

        except Exception as e:
            messagebox.showerror("錯誤", f"匯出失敗:\n{str(e)}")
            logger.error(f"匯出統計失敗: {e}")

    def reset_statistics(self):
        """重設統計數據"""
        result = messagebox.askyesno("確認", "確定要重設所有統計數據嗎？")
        if result:
            self.statistics = {
                'total_detections': 0,
                'detection_history': [],
                'region_stats': {},
                'session_start': datetime.now()
            }

            self.update_statistics_display()
            self.init_charts()
            self.update_status("統計數據已重設")
            logger.info("統計數據已重設")

    def show_help(self):
        """顯示使用說明"""
        help_text = """頭部檢測系統使用說明

基本操作：
1. 點擊「選擇圖片」或「選擇影片」載入要檢測的檔案
2. 調整檢測參數（信心度閾值、模型類型等）
3. 點擊「開始檢測」執行頭部檢測
4. 查看檢測結果和統計資訊

參數說明：
• 信心度閾值：控制檢測的敏感度，值越高越嚴格
• 模型類型：選擇使用的檢測模型
• 運算設備：選擇使用 GPU 或 CPU 進行運算

功能特色：
• 支援圖片和影片檢測
• 即時顯示檢測結果
• 統計數據收集和圖表顯示
• 檢測結果儲存和匯出
• GPU 加速運算

支援格式：
• 圖片：JPG, PNG, BMP, TIFF, WebP
• 影片：MP4, AVI, MOV, MKV, WMV, FLV

注意事項：
• 首次使用時會自動下載模型檔案
• GPU 模式需要 CUDA 支援
• 大型影片檔案處理時間較長
"""

        # 建立說明視窗
        help_window = tk.Toplevel(self.root)
        help_window.title("使用說明")
        help_window.geometry("600x500")
        help_window.resizable(False, False)

        # 建立文字框和捲軸
        text_frame = ttk.Frame(help_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Microsoft YaHei', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)

        # 關閉按鈕
        ttk.Button(help_window, text="關閉", command=help_window.destroy).pack(pady=10)

    def show_about(self):
        """顯示關於對話框"""
        about_text = """頭部檢測系統 v1.0

基於 YOLOv5 的 GPU 優化頭部檢測系統
提供圖形化使用者介面，支援圖片和影片檢測

主要特色：
• GPU 加速運算
• 即時檢測結果顯示
• 統計數據分析
• 多種檔案格式支援
• 檢測結果匯出

技術架構：
• 深度學習框架：PyTorch + YOLOv5
• GUI 框架：Tkinter
• 圖表繪製：Matplotlib
• 影像處理：OpenCV

開發資訊：
版本：1.0
更新日期：2024年
"""
        messagebox.showinfo("關於", about_text)

    def connect_rtsp(self):
        """連接 RTSP 串流"""
        if self.is_rtsp_streaming:
            messagebox.showinfo("提示", "RTSP 串流已在運行中")
            return

        rtsp_url = self.rtsp_url.get().strip()
        if not rtsp_url:
            messagebox.showwarning("警告", "請輸入 RTSP 串流地址")
            return

        if self.detector is None:
            messagebox.showerror("錯誤", "檢測器未初始化")
            return

        try:
            # 導入 RTSP 處理器
            from gpu_optimized_head_detection import GPUOptimizedRTSPProcessor

            self.rtsp_processor = GPUOptimizedRTSPProcessor(
                rtsp_url=rtsp_url,
                detector=self.detector,
                save_video=False,
                detection_interval=5  # 每5幀檢測一次
            )

            # 測試連接
            if self.rtsp_processor.connect_stream():
                self.is_rtsp_streaming = True
                self.rtsp_connect_button.config(state=tk.DISABLED)
                self.rtsp_disconnect_button.config(state=tk.NORMAL)

                # 清除其他輸入源
                self.current_image = None
                self.current_video = None

                # 在新線程中開始串流處理
                self.rtsp_thread = threading.Thread(target=self.rtsp_worker)
                self.rtsp_thread.daemon = True
                self.rtsp_thread.start()

                self.update_status(f"RTSP 串流已連接: {rtsp_url}")
                logger.info(f"RTSP 串流連接成功: {rtsp_url}")
            else:
                raise Exception("無法連接到 RTSP 串流")

        except Exception as e:
            messagebox.showerror("錯誤", f"RTSP 連接失敗:\n{str(e)}")
            logger.error(f"RTSP 連接失敗: {e}")

    def disconnect_rtsp(self):
        """斷開 RTSP 串流"""
        if not self.is_rtsp_streaming:
            return

        self.is_rtsp_streaming = False

        if self.rtsp_processor:
            self.rtsp_processor.cleanup()
            self.rtsp_processor = None

        self.rtsp_connect_button.config(state=tk.NORMAL)
        self.rtsp_disconnect_button.config(state=tk.DISABLED)

        self.update_status("RTSP 串流已斷開")
        logger.info("RTSP 串流已斷開")

    def rtsp_worker(self):
        """RTSP 串流工作線程"""
        try:
            frame_count = 0
            while self.is_rtsp_streaming and self.rtsp_processor:
                ret, frame = self.rtsp_processor.cap.read()
                if not ret:
                    logger.warning("無法讀取 RTSP 幀")
                    time.sleep(0.1)
                    continue

                frame_count += 1

                # 每5幀檢測一次
                if frame_count % 5 == 1:
                    try:
                        # 執行檢測
                        results = self.detector.detect_heads_batch([frame], roi_regions=self.roi_regions)
                        result = results[0]

                        total_count = result['total_count']
                        annotated_frame = result['annotated_frame']

                        # 繪製 ROI 區域
                        if self.roi_regions:
                            annotated_frame = self.draw_roi_on_frame(annotated_frame)

                        # 更新統計
                        self.update_detection_statistics(total_count, result['region_results'])

                        # 將結果放入隊列
                        self.result_queue.put(('rtsp_frame', {
                            'frame_count': frame_count,
                            'count': total_count,
                            'annotated_image': annotated_frame,
                            'original_image': frame
                        }))

                    except Exception as e:
                        logger.error(f"RTSP 檢測失敗: {e}")

                # 控制幀率
                time.sleep(0.03)  # 約30fps

        except Exception as e:
            logger.error(f"RTSP 工作線程錯誤: {e}")
            self.result_queue.put(('error', f"RTSP 串流錯誤: {str(e)}"))
        finally:
            if self.is_rtsp_streaming:
                self.result_queue.put(('rtsp_disconnected', None))

    def toggle_roi_mode(self):
        """切換 ROI 編輯模式"""
        self.roi_mode = not self.roi_mode
        if self.roi_mode:
            self.roi_mode_button.config(text="結束繪製 ROI")
            self.current_roi_points = []
            self.update_status("ROI 編輯模式：點擊畫布繪製區域")
            messagebox.showinfo("ROI 編輯", "ROI 編輯模式已開啟\n\n操作說明：\n• 左鍵點擊：添加 ROI 點\n• 右鍵點擊：完成當前區域\n• 至少需要3個點才能形成區域")
        else:
            self.roi_mode_button.config(text="開始繪製 ROI")
            self.update_status("ROI 編輯模式已關閉")

        self.update_roi_status()

    def on_canvas_click(self, event):
        """畫布左鍵點擊事件"""
        if not self.roi_mode:
            return

        # 獲取畫布上的實際座標
        canvas_x = self.result_canvas.canvasx(event.x)
        canvas_y = self.result_canvas.canvasy(event.y)

        # 轉換為圖片座標
        image_x, image_y = self.canvas_to_image_coords(canvas_x, canvas_y)
        if image_x is not None and image_y is not None:
            self.current_roi_points.append([int(image_x), int(image_y)])
            self.update_status(f"已添加 ROI 點: ({int(image_x)}, {int(image_y)}) - 共 {len(self.current_roi_points)} 個點")
            self.redraw_current_frame()

    def on_canvas_right_click(self, event):
        """畫布右鍵點擊事件"""
        if not self.roi_mode or len(self.current_roi_points) < 3:
            return

        # 完成當前 ROI 區域
        region_id = len(self.roi_regions)
        region_name = f"Region_{region_id + 1}"
        new_region = {
            'id': region_id,
            'name': region_name,
            'points': self.current_roi_points.copy(),
            'closed': True,
            'created_at': datetime.now().isoformat()
        }

        self.roi_regions.append(new_region)
        self.current_roi_points = []

        self.update_status(f"ROI 區域 '{region_name}' 已建立")
        self.update_roi_status()
        self.redraw_current_frame()

        messagebox.showinfo("ROI 建立", f"ROI 區域 '{region_name}' 已成功建立\n包含 {len(new_region['points'])} 個點")

    def on_canvas_motion(self, event):
        """畫布鼠標移動事件"""
        if self.roi_mode and len(self.current_roi_points) > 0:
            # 顯示當前繪製狀態
            canvas_x = self.result_canvas.canvasx(event.x)
            canvas_y = self.result_canvas.canvasy(event.y)
            image_x, image_y = self.canvas_to_image_coords(canvas_x, canvas_y)
            if image_x is not None and image_y is not None:
                self.update_status(f"ROI 繪製中: ({int(image_x)}, {int(image_y)}) - 已有 {len(self.current_roi_points)} 個點")

    def canvas_to_image_coords(self, canvas_x, canvas_y):
        """將畫布座標轉換為圖片座標"""
        try:
            # 獲取當前顯示的圖片
            current_frame = None
            if hasattr(self, 'last_annotated_frame') and self.last_annotated_frame is not None:
                current_frame = self.last_annotated_frame
            elif self.current_image is not None:
                current_frame = self.current_image

            if current_frame is None:
                return None, None

            # 獲取圖片和畫布尺寸
            img_height, img_width = current_frame.shape[:2]
            canvas_width = self.result_canvas.winfo_width()
            canvas_height = self.result_canvas.winfo_height()

            # 計算縮放比例
            scale_w = 600 / img_width  # 假設最大顯示寬度為600
            scale_h = 400 / img_height  # 假設最大顯示高度為400
            scale = min(scale_w, scale_h, 1.0)

            display_width = int(img_width * scale)
            display_height = int(img_height * scale)

            # 計算圖片在畫布中的偏移
            offset_x = (canvas_width - display_width) // 2
            offset_y = (canvas_height - display_height) // 2

            # 轉換座標
            if (canvas_x >= offset_x and canvas_x <= offset_x + display_width and
                canvas_y >= offset_y and canvas_y <= offset_y + display_height):

                image_x = (canvas_x - offset_x) / scale
                image_y = (canvas_y - offset_y) / scale
                return image_x, image_y

            return None, None

        except Exception as e:
            logger.error(f"座標轉換失敗: {e}")
            return None, None

    def clear_roi(self):
        """清除所有 ROI 區域"""
        if not self.roi_regions and not self.current_roi_points:
            messagebox.showinfo("提示", "沒有 ROI 區域需要清除")
            return

        result = messagebox.askyesno("確認", "確定要清除所有 ROI 區域嗎？")
        if result:
            self.roi_regions = []
            self.current_roi_points = []
            self.update_roi_status()
            self.redraw_current_frame()
            self.update_status("所有 ROI 區域已清除")

    def load_roi(self):
        """載入 ROI 區域"""
        filename = filedialog.askopenfilename(
            title="載入 ROI 區域",
            filetypes=[
                ('JSON檔案', '*.json'),
                ('所有檔案', '*.*')
            ]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 支援多種格式
                if isinstance(data, list):
                    # 舊格式：直接是點列表
                    if len(data) > 0 and isinstance(data[0], list):
                        self.roi_regions = [{
                            'id': 0,
                            'name': 'Region_1',
                            'points': data,
                            'closed': True,
                            'created_at': datetime.now().isoformat()
                        }]
                    else:
                        self.roi_regions = data
                elif isinstance(data, dict):
                    # 新格式：包含區域列表
                    self.roi_regions = data.get('regions', [])

                self.update_roi_status()
                self.redraw_current_frame()
                self.update_status(f"已載入 {len(self.roi_regions)} 個 ROI 區域")
                messagebox.showinfo("成功", f"已載入 {len(self.roi_regions)} 個 ROI 區域")

            except Exception as e:
                messagebox.showerror("錯誤", f"載入 ROI 失敗:\n{str(e)}")
                logger.error(f"載入 ROI 失敗: {e}")

    def save_roi(self):
        """儲存 ROI 區域"""
        if not self.roi_regions:
            messagebox.showwarning("警告", "沒有 ROI 區域可儲存")
            return

        filename = filedialog.asksaveasfilename(
            title="儲存 ROI 區域",
            defaultextension=".json",
            filetypes=[
                ('JSON檔案', '*.json'),
                ('所有檔案', '*.*')
            ]
        )

        if filename:
            try:
                data = {
                    'regions': self.roi_regions,
                    'created_at': datetime.now().isoformat(),
                    'total_regions': len(self.roi_regions)
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)

                self.update_status(f"ROI 區域已儲存: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"已儲存 {len(self.roi_regions)} 個 ROI 區域")

            except Exception as e:
                messagebox.showerror("錯誤", f"儲存 ROI 失敗:\n{str(e)}")
                logger.error(f"儲存 ROI 失敗: {e}")

    def update_roi_status(self):
        """更新 ROI 狀態顯示"""
        if self.roi_regions:
            status_text = f"ROI 狀態: {len(self.roi_regions)} 個區域"
        else:
            status_text = "ROI 狀態: 無區域"

        if self.roi_mode:
            status_text += f" (編輯中: {len(self.current_roi_points)} 點)"

        self.roi_status_label.config(text=status_text)

    def draw_roi_on_frame(self, frame):
        """在幀上繪製 ROI 區域"""
        try:
            if not self.roi_regions and not self.current_roi_points:
                return frame

            annotated_frame = frame.copy()

            # 定義顏色
            colors = [
                (0, 255, 255),  # 黃色
                (0, 255, 0),    # 綠色
                (255, 0, 0),    # 藍色
                (255, 0, 255),  # 洋紅
                (255, 255, 0),  # 青色
                (128, 0, 128),  # 紫色
                (255, 165, 0),  # 橙色
                (0, 128, 255),  # 橙藍色
            ]

            # 繪製已完成的 ROI 區域
            for i, region in enumerate(self.roi_regions):
                if len(region['points']) > 2:
                    color = colors[i % len(colors)]
                    roi_np = np.array([region['points']], dtype=np.int32)
                    cv2.polylines(annotated_frame, roi_np, isClosed=True, color=color, thickness=2)

                    # 添加區域標籤
                    if len(region['points']) > 0:
                        label_pos = tuple(region['points'][0])
                        cv2.putText(annotated_frame, region['name'],
                                  (label_pos[0], label_pos[1] - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # 繪製當前正在繪製的 ROI
            if self.current_roi_points:
                current_color = (0, 0, 255)  # 紅色

                if len(self.current_roi_points) > 1:
                    roi_np = np.array([self.current_roi_points], dtype=np.int32)
                    cv2.polylines(annotated_frame, roi_np, isClosed=False, color=current_color, thickness=2)

                # 繪製所有點
                for point in self.current_roi_points:
                    cv2.circle(annotated_frame, tuple(point), 5, current_color, -1)

            return annotated_frame

        except Exception as e:
            logger.error(f"繪製 ROI 失敗: {e}")
            return frame

    def redraw_current_frame(self):
        """重新繪製當前幀"""
        try:
            if hasattr(self, 'last_annotated_frame') and self.last_annotated_frame is not None:
                # 重新繪製 ROI
                frame_with_roi = self.draw_roi_on_frame(self.last_annotated_frame)
                self.display_frame_on_canvas(frame_with_roi, self.result_canvas)
            elif self.current_image is not None:
                # 如果有原始圖片，在上面繪製 ROI
                frame_with_roi = self.draw_roi_on_frame(self.current_image)
                self.display_frame_on_canvas(frame_with_roi, self.result_canvas)
        except Exception as e:
            logger.error(f"重新繪製幀失敗: {e}")

    def display_frame_on_canvas(self, frame, canvas):
        """在指定畫布上顯示幀"""
        try:
            # 調整圖片大小
            display_image = self.resize_image_for_display(frame)

            # 轉換為 PIL 格式
            image_rgb = cv2.cvtColor(display_image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            photo = ImageTk.PhotoImage(pil_image)

            # 清除畫布並顯示圖片
            canvas.delete("all")
            canvas_width = canvas.winfo_width()
            canvas_height = canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                x = (canvas_width - photo.width()) // 2
                y = (canvas_height - photo.height()) // 2
                canvas.create_image(x, y, anchor=tk.NW, image=photo)
                canvas.image = photo  # 保持引用

        except Exception as e:
            logger.error(f"顯示幀失敗: {e}")


def main():
    """主程式入口"""
    try:
        # 建立主視窗
        root = tk.Tk()

        # 設置視窗圖示（如果有的話）
        try:
            root.iconbitmap('icon.ico')
        except:
            pass  # 忽略圖示載入錯誤

        # 建立應用程式
        app = HeadDetectionGUI(root)

        # 設置關閉事件處理
        def on_closing():
            if app.is_processing or app.is_rtsp_streaming:
                message = "檢測正在進行中，確定要退出嗎？"
                if app.is_rtsp_streaming:
                    message = "RTSP 串流正在運行中，確定要退出嗎？"
                result = messagebox.askyesno("確認", message)
                if not result:
                    return
                app.stop_detection()
                app.disconnect_rtsp()

            root.quit()
            root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        # 啟動主循環
        root.mainloop()

    except Exception as e:
        logger.error(f"應用程式啟動失敗: {e}")
        messagebox.showerror("錯誤", f"應用程式啟動失敗:\n{str(e)}")


if __name__ == "__main__":
    main()
