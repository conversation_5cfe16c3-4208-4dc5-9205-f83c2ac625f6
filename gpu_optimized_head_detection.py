#!/usr/bin/env python3
"""
GPU优化的YOLOv5头部检测系统
专门针对GPU加速优化的RTSP视频流头部检测
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GPUOptimizedHeadDetector:
    def __init__(self, model_type='person', conf_threshold=0.4, device='cuda', batch_size=1):
        """
        GPU优化的头部检测器
        
        Args:
            model_type: 模型类型
            conf_threshold: 置信度阈值
            device: 运行设备
            batch_size: 批处理大小（GPU优化）
        """
        self.conf_threshold = conf_threshold
        self.device = device
        self.model_type = model_type
        self.batch_size = batch_size
        
        # 设置GPU优化参数
        self._setup_gpu_optimization()
        
        # 加载模型
        self._load_model()
        
    def _setup_gpu_optimization(self):
        """设置GPU优化参数"""
        if self.device == 'cuda' and torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            torch.cuda.empty_cache()
            
            gpu_count = torch.cuda.device_count()
            current_gpu = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_gpu)
            gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
            
            logger.info(f"GPU优化设置:")
            logger.info(f"  - GPU数量: {gpu_count}")
            logger.info(f"  - 当前GPU: {current_gpu} ({gpu_name})")
            logger.info(f"  - GPU内存: {gpu_memory:.1f} GB")
            logger.info(f"  - CUDA版本: {torch.version.cuda}")
            logger.info(f"  - cuDNN启用: {torch.backends.cudnn.enabled}")
            logger.info(f"  - cuDNN基准: {torch.backends.cudnn.benchmark}")
            
        else:
            logger.warning("GPU不可用，使用CPU模式")
            self.device = 'cpu'
    
    def _load_model(self):
        """加载并优化模型"""
        try:
            logger.info("加载YOLOv5模型...")
            
            if self.model_type == 'custom':
                try:
                    self.model = torch.hub.load('ultralytics/yolov5', 'custom', 
                                               path='yolov5s-head.pt', force_reload=True)
                    logger.info("加载专门的头部检测模型")
                except Exception:
                    logger.warning("未找到头部检测模型，使用人体检测模型")
                    self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
                    self.model_type = 'person'
            else:
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            
            self.model.to(self.device)
            self.model.conf = self.conf_threshold
            self.model.iou = 0.45
            self.model.max_det = 1000
            
            if self.device == 'cuda':
                self.model.half()
                logger.info("启用FP16半精度推理")
                self._warmup_gpu()
            
            logger.info(f"模型加载完成，设备: {self.device}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _warmup_gpu(self):
        """GPU预热以获得稳定性能"""
        logger.info("GPU预热中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        with torch.no_grad():
            for _ in range(3):
                _ = self.model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("GPU预热完成")
    
    def _extract_head_region(self, frame, person_bbox, head_ratio=0.15):
        """从人体检测框中提取头部区域"""
        x1, y1, x2, y2 = person_bbox
        person_height = y2 - y1
        person_width = x2 - x1
        head_height = int(person_height * head_ratio)
        head_width_margin = int(person_width * 0.1)
        head_x1 = max(0, x1 + head_width_margin)
        head_y1 = max(0, y1)
        head_x2 = min(frame.shape[1], x2 - head_width_margin)
        head_y2 = min(frame.shape[0], y1 + head_height)
        return [head_x1, head_y1, head_x2, head_y2]
    
    def detect_heads_batch(self, frames, roi_regions=None):
        """批量檢測頭部（GPU優化，支持多區域）"""
        if not isinstance(frames, list):
            frames = [frames]

        results_list = []

        try:
            with torch.no_grad():
                if self.device == 'cuda':
                    torch.cuda.synchronize()
                results = self.model(frames)
                if self.device == 'cuda':
                    torch.cuda.synchronize()

            for i, frame in enumerate(frames):
                frame_results = results if len(frames) == 1 else [results[i]]
                multi_region_results = self._process_multi_region_frame(
                    frame, frame_results, roi_regions)
                results_list.append(multi_region_results)

            return results_list

        except Exception as e:
            logger.error(f"批量檢測過程中出錯: {e}")
            return [{'total_count': 0, 'region_results': [], 'annotated_frame': frame} for frame in frames]
    
    def _process_multi_region_frame(self, frame, results, roi_regions=None):
        """處理多區域檢測結果"""
        try:
            detections = results.pandas().xyxy[0]
            all_head_bboxes = []
            annotated_frame = frame.copy()

            # 獲取所有檢測結果
            if self.model_type == 'custom':
                target_detections = detections[detections['name'].isin(['head', 'person'])]
            else:
                target_detections = detections[detections['class'].isin([0, 25])]

            for _, detection in target_detections.iterrows():
                confidence = detection['confidence']
                if self.model_type == 'custom' and detection['name'] == 'head':
                    bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                else:
                    person_bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                    bbox = self._extract_head_region(frame, person_bbox)
                all_head_bboxes.append(bbox + [confidence])

            # 處理多個ROI區域
            region_results = []
            total_count = 0

            if roi_regions and len(roi_regions) > 0:
                for region in roi_regions:
                    if region['closed'] and len(region['points']) > 2:
                        region_bboxes = self._filter_detections_by_region(all_head_bboxes, region['points'])
                        region_count = len(region_bboxes)
                        total_count += region_count

                        region_result = {
                            'region_id': region['id'],
                            'region_name': region['name'],
                            'count': region_count,
                            'bboxes': region_bboxes,
                            'points': region['points']
                        }
                        region_results.append(region_result)
            else:
                # 如果沒有ROI區域，使用所有檢測結果
                total_count = len(all_head_bboxes)
                region_results.append({
                    'region_id': -1,
                    'region_name': 'Full_Frame',
                    'count': total_count,
                    'bboxes': all_head_bboxes,
                    'points': []
                })

            # 繪製檢測結果
            annotated_frame = self._draw_multi_region_results(annotated_frame, region_results, total_count)

            return {
                'total_count': total_count,
                'region_results': region_results,
                'annotated_frame': annotated_frame
            }

        except Exception as e:
            logger.error(f"處理多區域檢測結果時出錯: {e}")
            return {
                'total_count': 0,
                'region_results': [],
                'annotated_frame': frame
            }

    def _filter_detections_by_region(self, head_bboxes, region_points):
        """根據區域過濾檢測結果"""
        filtered_bboxes = []
        roi_np = np.array(region_points, dtype=np.int32)

        for bbox in head_bboxes:
            x1, y1, x2, y2, confidence = bbox
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            if cv2.pointPolygonTest(roi_np, (center_x, center_y), False) >= 0:
                filtered_bboxes.append(bbox)

        return filtered_bboxes

    def _draw_multi_region_results(self, frame, region_results, total_count):
        """繪製多區域檢測結果"""
        # 定義不同區域的顏色
        colors = [
            (0, 0, 255),    # 紅色
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色
            (0, 255, 255),  # 黃色
            (255, 0, 255),  # 洋紅
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]

        global_head_id = 1

        for i, region_result in enumerate(region_results):
            color = colors[i % len(colors)]

            # 繪製該區域的檢測框（使用與ROI區域一致的顏色，最細線條）
            for bbox in region_result['bboxes']:
                x1, y1, x2, y2, confidence = bbox
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 1)

                # ID 標籤
                label = f'{global_head_id}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                cv2.rectangle(frame, (x1, y1 - label_size[1] - 8),
                            (x1 + label_size[0] + 6, y1), color, -1)
                cv2.putText(frame, label, (x1 + 3, y1 - 4),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                # 信心值
                conf_text = f'{confidence:.2f}'
                cv2.putText(frame, conf_text, (x1 + 3, y1 - label_size[1] - 12),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 255, 255), 1)

                # 中心點
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                cv2.circle(frame, (center_x, center_y), 3, color, -1)

                global_head_id += 1

        # 顯示總計數
        total_text = f'Total Count: {total_count}'
        cv2.putText(frame, total_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)

        # 顯示各區域計數
        y_offset = 70
        for region_result in region_results:
            if region_result['region_id'] >= 0:  # 跳過全幀結果
                region_text = f"{region_result['region_name']}: {region_result['count']}"
                cv2.putText(frame, region_text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
                y_offset += 25

        # 模式和GPU信息
        mode_text = f'Mode: {self.model_type.upper()} (GPU) - Multi-Region'
        cv2.putText(frame, mode_text, (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)

        if self.device == 'cuda':
            gpu_memory = torch.cuda.memory_allocated() / 1024**2
            gpu_text = f'GPU Memory: {gpu_memory:.0f}MB'
            cv2.putText(frame, gpu_text, (10, y_offset + 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # 時間戳
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cv2.putText(frame, timestamp, (10, frame.shape[0] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        return frame

    def _process_single_frame_result(self, frame, results, roi_points=None):
        """处理单帧检测结果"""
        try:
            detections = results.pandas().xyxy[0]
            head_bboxes = []
            annotated_frame = frame.copy()
            
            if self.model_type == 'custom':
                target_detections = detections[detections['name'].isin(['head', 'person'])]
            else:
                # 0: person, 25: umbrella
                target_detections = detections[detections['class'].isin([0, 25])]

            for _, detection in target_detections.iterrows():
                confidence = detection['confidence']
                if self.model_type == 'custom' and detection['name'] == 'head':
                    bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                else: # Person or Umbrella
                    person_bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                    bbox = self._extract_head_region(frame, person_bbox)
                head_bboxes.append(bbox + [confidence])

            # 如果定义了ROI，则过滤检测结果
            if roi_points and len(roi_points) > 2:
                filtered_bboxes = []
                roi_np = np.array(roi_points, dtype=np.int32)
                for bbox in head_bboxes:
                    x1, y1, x2, y2, _ = bbox
                    center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                    if cv2.pointPolygonTest(roi_np, (center_x, center_y), False) >= 0:
                        filtered_bboxes.append(bbox)
                head_bboxes = filtered_bboxes

            # 绘制检测结果
            head_count = len(head_bboxes)
            
            for i, (x1, y1, x2, y2, confidence) in enumerate(head_bboxes):
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 0, 255), 1)
                label = f'{i+1}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)[0]
                # ID 標籤底色
                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 6), 
                            (x1 + label_size[0] + 4, y1), (0, 0, 255), -1)
                # ID 文字
                cv2.putText(annotated_frame, label, (x1 + 2, y1 - 3), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                # 信心值（白色小字）
                conf_text = f'{confidence:.2f}'
                conf_size = cv2.getTextSize(conf_text, cv2.FONT_HERSHEY_SIMPLEX, 0.35, 1)[0]
                conf_y = y1 - label_size[1] - 8  # 再往上
                cv2.putText(annotated_frame, conf_text, (x1 + 2, conf_y), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 255, 255), 1)
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                cv2.circle(annotated_frame, (center_x, center_y), 3, (255, 0, 0), -1)
            
            count_text = f'Person Count: {head_count}'
            cv2.putText(annotated_frame, count_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
            
            mode_text = f'Mode: {self.model_type.upper()} (GPU)'
            cv2.putText(annotated_frame, mode_text, (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            
            if self.device == 'cuda':
                gpu_memory = torch.cuda.memory_allocated() / 1024**2
                gpu_text = f'GPU Memory: {gpu_memory:.0f}MB'
                cv2.putText(annotated_frame, gpu_text, (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(annotated_frame, timestamp, (10, annotated_frame.shape[0] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return head_count, head_bboxes, annotated_frame
            
        except Exception as e:
            logger.error(f"处理帧结果时出错: {e}")
            return 0, [], frame

class GPUOptimizedRTSPProcessor:
    def __init__(self, rtsp_url, detector, save_video=False, output_path='gpu_head_detection.mp4', detection_interval=1):
        self.rtsp_url = rtsp_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.cap = None
        self.out = None
        self.frame_times = []
        self.gpu_memory_usage = []

        # 檢測間隔控制
        self.detection_interval = detection_interval
        self.last_detection_result = None  # 緩存上次檢測結果
        self.detection_frame_count = 0  # 檢測幀計數器

        # 多區域 ROI 设置
        self.roi_file = "multi_roi_regions.json"
        self.roi_regions = self._load_multi_roi()  # 存儲多個區域
        self.current_region_points = []  # 當前正在繪製的區域點
        self.current_region_closed = False  # 當前區域是否已關閉
        self.selected_region_index = -1  # 當前選中的區域索引（用於編輯）
        self.window_name = 'GPU_Head_Detection'

        # 統計數據
        self.region_statistics = {}  # 每個區域的統計數據
        self.total_statistics = {
            'total_count': 0,
            'region_counts': [],
            'detection_history': []
        }

        logger.info(f"檢測間隔設置為: 每 {self.detection_interval} 幀檢測一次")

    def _load_multi_roi(self):
        """從文件加載多個ROI區域"""
        if os.path.exists(self.roi_file):
            try:
                with open(self.roi_file, 'r') as f:
                    data = json.load(f)
                    # 兼容舊格式：如果是點列表，轉換為新格式
                    if isinstance(data, list) and len(data) > 0 and isinstance(data[0], list):
                        regions = [{'id': 0, 'name': 'Region_1', 'points': data, 'closed': True}]
                        logger.info(f"轉換舊格式ROI到新的多區域格式")
                    else:
                        regions = data.get('regions', [])
                    logger.info(f"成功從 {self.roi_file} 加載 {len(regions)} 個ROI區域")
                    return regions
            except Exception as e:
                logger.error(f"加載ROI文件失敗: {e}")
        return []

    def _save_multi_roi(self):
        """保存多個ROI區域到文件"""
        try:
            data = {
                'regions': self.roi_regions,
                'created_at': datetime.now().isoformat(),
                'total_regions': len(self.roi_regions)
            }
            with open(self.roi_file, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"已保存 {len(self.roi_regions)} 個ROI區域到 {self.roi_file}")
        except Exception as e:
            logger.error(f"保存ROI文件失敗: {e}")

    def _add_new_region(self):
        """添加新的ROI區域"""
        if len(self.current_region_points) > 2:
            region_id = len(self.roi_regions)
            region_name = f"Region_{region_id + 1}"
            new_region = {
                'id': region_id,
                'name': region_name,
                'points': self.current_region_points.copy(),
                'closed': True,
                'created_at': datetime.now().isoformat()
            }
            self.roi_regions.append(new_region)
            self.current_region_points = []
            self.current_region_closed = False
            self._save_multi_roi()
            logger.info(f"添加新區域: {region_name} (共 {len(self.roi_regions)} 個區域)")
            return region_id
        return -1

    def _mouse_callback(self, event, x, y, flags, param):
        """鼠標回調函數用於繪製多個ROI區域"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 左鍵點擊：添加點到當前區域
            if self.current_region_closed:
                logger.warning("當前區域已關閉，請按 'n' 開始新區域或 'c' 清除所有區域")
                return
            self.current_region_points.append([x, y])
            logger.info(f"添加ROI點到當前區域: ({x}, {y}) - 點數: {len(self.current_region_points)}")

        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右鍵點擊：關閉當前區域
            if not self.current_region_closed and len(self.current_region_points) > 2:
                region_id = self._add_new_region()
                if region_id >= 0:
                    self.current_region_closed = True
                    logger.info(f"區域 {region_id + 1} 已關閉並保存")
                else:
                    logger.warning("區域點數不足，無法關閉")

        elif event == cv2.EVENT_MBUTTONDOWN:
            # 中鍵點擊：選擇/取消選擇區域（用於編輯）
            self._select_region_at_point(x, y)

    def _select_region_at_point(self, x, y):
        """選擇指定點所在的區域"""
        for i, region in enumerate(self.roi_regions):
            if len(region['points']) > 2:
                roi_np = np.array(region['points'], dtype=np.int32)
                if cv2.pointPolygonTest(roi_np, (x, y), False) >= 0:
                    self.selected_region_index = i
                    logger.info(f"選中區域 {i + 1}: {region['name']}")
                    return
        self.selected_region_index = -1
        logger.info("未選中任何區域")

    def _clear_all_roi(self):
        """清除所有ROI區域"""
        self.roi_regions = []
        self.current_region_points = []
        self.current_region_closed = False
        self.selected_region_index = -1
        self.region_statistics = {}
        if os.path.exists(self.roi_file):
            os.remove(self.roi_file)
        logger.info("所有ROI區域已清除")

    def _start_new_region(self):
        """開始繪製新區域"""
        if not self.current_region_closed and len(self.current_region_points) > 0:
            logger.warning("當前區域未完成，請先右鍵關閉當前區域")
            return
        self.current_region_points = []
        self.current_region_closed = False
        logger.info("開始繪製新區域")

    def _delete_selected_region(self):
        """刪除選中的區域"""
        if self.selected_region_index >= 0 and self.selected_region_index < len(self.roi_regions):
            deleted_region = self.roi_regions.pop(self.selected_region_index)
            # 重新分配ID
            for i, region in enumerate(self.roi_regions):
                region['id'] = i
                region['name'] = f"Region_{i + 1}"
            self.selected_region_index = -1
            self._save_multi_roi()
            logger.info(f"已刪除區域: {deleted_region['name']}")
        else:
            logger.warning("沒有選中的區域可刪除")

    def _update_statistics(self, region_results, total_count):
        """更新統計數據"""
        # 更新總統計
        self.total_statistics['total_count'] = total_count
        self.total_statistics['region_counts'] = [r['count'] for r in region_results]
        self.total_statistics['detection_history'].append({
            'timestamp': datetime.now().isoformat(),
            'total_count': total_count,
            'region_counts': {r['region_name']: r['count'] for r in region_results}
        })

        # 保持歷史記錄在合理範圍內
        if len(self.total_statistics['detection_history']) > 1000:
            self.total_statistics['detection_history'] = self.total_statistics['detection_history'][-1000:]

        # 更新各區域統計
        for region_result in region_results:
            region_name = region_result['region_name']
            if region_name not in self.region_statistics:
                self.region_statistics[region_name] = {
                    'total_detections': 0,
                    'max_count': 0,
                    'avg_count': 0,
                    'detection_count': 0
                }

            stats = self.region_statistics[region_name]
            stats['total_detections'] += region_result['count']
            stats['max_count'] = max(stats['max_count'], region_result['count'])
            stats['detection_count'] += 1
            stats['avg_count'] = stats['total_detections'] / stats['detection_count']

    def _get_statistics_summary(self):
        """獲取統計摘要"""
        summary = {
            'total_regions': len(self.roi_regions),
            'current_total': self.total_statistics['total_count'],
            'region_stats': self.region_statistics.copy()
        }
        return summary

    def _draw_roi_regions(self, frame):
        """繪製所有ROI區域"""
        # 定義不同區域的顏色
        colors = [
            (0, 255, 255),  # 黃色
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色
            (255, 0, 255),  # 洋紅
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
            (0, 128, 255),  # 橙藍色
        ]

        # 繪製已完成的區域
        for i, region in enumerate(self.roi_regions):
            if region['closed'] and len(region['points']) > 2:
                color = colors[i % len(colors)]
                roi_np = np.array([region['points']], dtype=np.int32)

                # 如果是選中的區域，使用更粗的線條
                thickness = 4 if i == self.selected_region_index else 2
                cv2.polylines(frame, roi_np, isClosed=True, color=color, thickness=thickness)

                # 添加區域標籤
                if len(region['points']) > 0:
                    label_pos = tuple(region['points'][0])
                    cv2.putText(frame, region['name'],
                              (label_pos[0], label_pos[1] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        # 繪製當前正在繪製的區域
        if len(self.current_region_points) > 0:
            current_color = (0, 0, 255)  # 紅色表示正在繪製

            if len(self.current_region_points) > 1:
                roi_np = np.array([self.current_region_points], dtype=np.int32)
                cv2.polylines(frame, roi_np, isClosed=False, color=current_color, thickness=2)

            # 繪製所有點
            for point in self.current_region_points:
                cv2.circle(frame, tuple(point), 5, current_color, -1)

            # 顯示當前點數
            if len(self.current_region_points) > 0:
                text = f"Drawing: {len(self.current_region_points)} points"
                cv2.putText(frame, text, (10, frame.shape[0] - 50),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, current_color, 2)

    def _print_region_info(self):
        """打印區域信息"""
        logger.info("=== 多區域ROI信息 ===")
        logger.info(f"總區域數: {len(self.roi_regions)}")

        for i, region in enumerate(self.roi_regions):
            status = "選中" if i == self.selected_region_index else "未選中"
            logger.info(f"區域 {i+1}: {region['name']} - 點數: {len(region['points'])} - 狀態: {status}")

        if len(self.current_region_points) > 0:
            logger.info(f"正在繪製: {len(self.current_region_points)} 個點")

        # 顯示統計信息
        stats = self._get_statistics_summary()
        logger.info(f"當前總人數: {stats['current_total']}")

        for region_name, region_stats in stats['region_stats'].items():
            logger.info(f"  {region_name}: 平均 {region_stats['avg_count']:.1f}, "
                       f"最大 {region_stats['max_count']}, "
                       f"檢測次數 {region_stats['detection_count']}")

    def _print_help(self):
        """打印幫助信息"""
        help_text = """
=== 多區域頭部檢測控制說明 ===
鼠標操作:
  左鍵點擊: 添加點到當前區域
  右鍵點擊: 完成當前區域繪製
  中鍵點擊: 選擇/取消選擇區域

鍵盤操作:
  'q': 退出程序
  'c': 清除所有ROI區域
  'n': 開始繪製新區域
  'd': 刪除選中的區域
  's': 保存當前幀
  'i': 顯示區域信息
  'h': 顯示此幫助信息

區域繪製流程:
1. 按 'n' 開始新區域
2. 左鍵點擊添加點 (至少3個點)
3. 右鍵點擊完成區域
4. 重複步驟1-3添加更多區域

檢測間隔控制:
- 當前間隔: 每 {self.detection_interval} 幀檢測一次
- DETECT: 執行實際檢測
- CACHED: 使用緩存結果
- 可通過 --detection-interval 參數調整
        """
        logger.info(help_text)

    def connect_stream(self):
        """连接RTSP流"""
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
            
            if not self.cap.isOpened():
                raise Exception("无法连接到RTSP流")
            
            fps = int(self.cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            logger.info(f"RTSP流连接成功: {width}x{height} @ {fps}fps")
            
            if self.save_video:
                fourcc = cv2.VideoWriter_fourcc(*'H264')
                self.out = cv2.VideoWriter(self.output_path, fourcc, fps, (width, height))
                logger.info(f"GPU优化视频将保存到: {self.output_path} (H.264格式)")
            
            return True
            
        except Exception as e:
            logger.error(f"连接RTSP流失败: {e}")
            return False
    
    def process_stream(self, display=True, max_frames=None):
        """GPU优化的流处理"""
        if not self.connect_stream():
            return

        if display:
            cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
            cv2.setMouseCallback(self.window_name, self._mouse_callback)

        frame_count = 0
        start_time = time.time()
        
        try:
            while True:
                frame_start = time.time()
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("无法读取帧，尝试重新连接...")
                    time.sleep(1)
                    continue
                
                frame_count += 1

                # 檢測間隔控制
                should_detect = (frame_count % self.detection_interval == 1)

                if should_detect:
                    # 執行檢測
                    detection_start = time.time()
                    results = self.detector.detect_heads_batch([frame], roi_regions=self.roi_regions)
                    multi_region_result = results[0]
                    detection_time = time.time() - detection_start

                    # 緩存檢測結果
                    self.last_detection_result = multi_region_result
                    self.detection_frame_count += 1
                else:
                    # 使用緩存的檢測結果
                    if self.last_detection_result is not None:
                        multi_region_result = self.last_detection_result.copy()
                        # 重新繪製當前幀
                        multi_region_result['annotated_frame'] = self.detector._draw_multi_region_results(
                            frame, multi_region_result['region_results'], multi_region_result['total_count'])
                    else:
                        # 如果沒有緩存結果，執行一次檢測
                        detection_start = time.time()
                        results = self.detector.detect_heads_batch([frame], roi_regions=self.roi_regions)
                        multi_region_result = results[0]
                        detection_time = time.time() - detection_start
                        self.last_detection_result = multi_region_result
                        self.detection_frame_count += 1
                    detection_time = 0  # 非檢測幀的檢測時間為0

                total_count = multi_region_result['total_count']
                region_results = multi_region_result['region_results']
                annotated_frame = multi_region_result['annotated_frame']

                # 更新統計數據（只在檢測幀更新）
                if should_detect:
                    self._update_statistics(region_results, total_count)
                
                frame_time = time.time() - frame_start
                self.frame_times.append(frame_time)
                
                if self.detector.device == 'cuda':
                    gpu_memory = torch.cuda.memory_allocated() / 1024**2
                    self.gpu_memory_usage.append(gpu_memory)
                
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                # 顯示FPS和檢測信息
                detection_status = "DETECT" if should_detect else "CACHED"
                fps_text = f'FPS: {fps:.1f} | Det: {detection_time*1000:.1f}ms | {detection_status}'
                cv2.putText(annotated_frame, fps_text, (10, 150),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

                # 顯示檢測間隔信息
                interval_text = f'Detection Interval: {self.detection_interval} frames'
                cv2.putText(annotated_frame, interval_text, (10, 180),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
                
                # 繪製多個ROI區域
                self._draw_roi_regions(annotated_frame)

                if display:
                    if annotated_frame is not None and isinstance(annotated_frame, np.ndarray):
                        cv2.imshow(self.window_name, annotated_frame)
                    else:
                        logger.warning("annotated_frame 無效，未顯示")
                    
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        break
                    elif key == ord('c'):
                        self._clear_all_roi()
                    elif key == ord('n'):
                        self._start_new_region()
                    elif key == ord('d'):
                        self._delete_selected_region()
                    elif key == ord('s'):
                        save_path = f"gpu_head_frame_{frame_count}.jpg"
                        cv2.imwrite(save_path, annotated_frame)
                        logger.info(f"保存帧到: {save_path}")
                    elif key == ord('i'):
                        self._print_region_info()
                    elif key == ord('h'):
                        self._print_help()
                
                if self.save_video and self.out and annotated_frame is not None and isinstance(annotated_frame, np.ndarray):
                    self.out.write(annotated_frame)
                
                if frame_count % 100 == 0:
                    avg_fps = frame_count / elapsed_time
                    avg_gpu_memory = np.mean(self.gpu_memory_usage[-100:]) if self.gpu_memory_usage else 0
                    region_summary = ", ".join([f"{r['region_name']}: {r['count']}" for r in region_results])
                    detection_efficiency = (self.detection_frame_count / frame_count) * 100 if frame_count > 0 else 0
                    logger.info(f"帧 {frame_count}: 總人數 {total_count} ({region_summary}), "
                              f"平均FPS {avg_fps:.1f}, "
                              f"檢測效率 {detection_efficiency:.1f}% ({self.detection_frame_count}/{frame_count}), "
                              f"GPU内存 {avg_gpu_memory:.1f}MB")
                
                if max_frames and frame_count >= max_frames:
                    break
                    
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"处理过程中出错: {e}")
        finally:
            self._print_performance_summary()
            self.cleanup()
    
    def _print_performance_summary(self):
        """打印性能和統計總結"""
        if self.frame_times:
            avg_frame_time = np.mean(self.frame_times)
            avg_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0

            logger.info("=== GPU性能總結 ===")
            logger.info(f"平均幀處理時間: {avg_frame_time*1000:.1f}ms")
            logger.info(f"平均FPS: {avg_fps:.1f}")
            logger.info(f"檢測間隔設置: 每 {self.detection_interval} 幀檢測一次")
            logger.info(f"實際檢測幀數: {self.detection_frame_count}")

            if self.gpu_memory_usage:
                avg_gpu_memory = np.mean(self.gpu_memory_usage)
                max_gpu_memory = max(self.gpu_memory_usage)
                logger.info(f"平均GPU內存使用: {avg_gpu_memory:.1f}MB")
                logger.info(f"峰值GPU內存使用: {max_gpu_memory:.1f}MB")

        # 多區域統計總結
        logger.info("=== 多區域檢測統計總結 ===")
        logger.info(f"總ROI區域數: {len(self.roi_regions)}")

        stats = self._get_statistics_summary()
        logger.info(f"最終總人數: {stats['current_total']}")

        for region_name, region_stats in stats['region_stats'].items():
            logger.info(f"區域 {region_name}:")
            logger.info(f"  - 平均人數: {region_stats['avg_count']:.1f}")
            logger.info(f"  - 最大人數: {region_stats['max_count']}")
            logger.info(f"  - 總檢測次數: {region_stats['detection_count']}")
            logger.info(f"  - 累計檢測人數: {region_stats['total_detections']}")

        # 保存統計數據到文件
        self._save_statistics_report()

    def _save_statistics_report(self):
        """保存統計報告到文件"""
        try:
            report_file = f"detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            report_data = {
                'session_info': {
                    'start_time': datetime.now().isoformat(),
                    'rtsp_url': self.rtsp_url,
                    'total_regions': len(self.roi_regions),
                    'device': self.detector.device,
                    'model_type': self.detector.model_type
                },
                'roi_regions': self.roi_regions,
                'statistics': self.total_statistics,
                'region_statistics': self.region_statistics,
                'performance': {
                    'avg_fps': 1.0 / np.mean(self.frame_times) if self.frame_times else 0,
                    'avg_frame_time_ms': np.mean(self.frame_times) * 1000 if self.frame_times else 0,
                    'avg_gpu_memory_mb': np.mean(self.gpu_memory_usage) if self.gpu_memory_usage else 0,
                    'max_gpu_memory_mb': max(self.gpu_memory_usage) if self.gpu_memory_usage else 0
                }
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

            logger.info(f"統計報告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"保存統計報告失敗: {e}")
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        if self.out:
            self.out.release()
        cv2.destroyAllWindows()
        cv2.waitKey(1)
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("资源清理完成")

def main():
    parser = argparse.ArgumentParser(description='GPU优化的YOLOv5头部检测系统')
    parser.add_argument('--rtsp-url', type=str, 
                       default='rtsp://root:Abc_123@*************:7040/axis-media/media.amp?resolution=1280x800',
                       help='RTSP流地址')
    parser.add_argument('--model-type', type=str, default='person',
                       choices=['custom', 'person'], help='模型类型')
    parser.add_argument('--conf-threshold', type=float, default=0.1,
                       help='检测置信度阈值')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='运行设备')
    parser.add_argument('--batch-size', type=int, default=1,
                       help='批处理大小（GPU优化）')
    parser.add_argument('--save-video', action='store_true',
                       help='保存输出视频')
    parser.add_argument('--output-path', type=str, default='gpu_head_detection.mp4',
                       help='输出视频路径')
    parser.add_argument('--no-display', action='store_true',
                       help='不显示视频窗口')
    parser.add_argument('--max-frames', type=int, default=None,
                       help='最大处理帧数')
    parser.add_argument('--detection-interval', type=int, default=1,
                       help='檢測間隔（幀數），例如5表示每5幀檢測一次，預設為1（每幀檢測）')

    args = parser.parse_args()

    # 驗證檢測間隔參數
    if args.detection_interval < 1:
        logger.warning("檢測間隔不能小於1，已重設為1")
        args.detection_interval = 1
    elif args.detection_interval > 30:
        logger.warning("檢測間隔過大，已限制為30幀")
        args.detection_interval = 30
    
    if args.device == 'cuda':
        if not torch.cuda.is_available():
            logger.error("CUDA不可用！请检查GPU驱动和CUDA安装")
            return 1
    
    try:
        detector = GPUOptimizedHeadDetector(
            model_type=args.model_type,
            conf_threshold=args.conf_threshold,
            device=args.device,
            batch_size=args.batch_size
        )
        
        processor = GPUOptimizedRTSPProcessor(
            rtsp_url=args.rtsp_url,
            detector=detector,
            save_video=args.save_video,
            output_path=args.output_path,
            detection_interval=args.detection_interval
        )
        
        processor.process_stream(
            display=not args.no_display,
            max_frames=args.max_frames
        )
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
