#!/usr/bin/env python3
"""
GUI 測試腳本
測試頭部檢測 GUI 的基本功能
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import numpy as np
import cv2

def test_imports():
    """測試必要套件導入"""
    print("測試套件導入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch 導入失敗: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV 導入失敗: {e}")
        return False
    
    try:
        from PIL import Image
        print(f"✅ Pillow: {Image.__version__}")
    except ImportError as e:
        print(f"❌ Pillow 導入失敗: {e}")
        return False
    
    try:
        import matplotlib
        print(f"✅ Matplotlib: {matplotlib.__version__}")
    except ImportError as e:
        print(f"❌ Matplotlib 導入失敗: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy 導入失敗: {e}")
        return False
    
    return True

def test_gui_creation():
    """測試 GUI 建立"""
    print("\n測試 GUI 建立...")
    
    try:
        # 建立測試視窗
        root = tk.Tk()
        root.title("GUI 測試")
        root.geometry("400x300")
        
        # 添加測試元件
        label = tk.Label(root, text="GUI 測試成功！", font=('Arial', 16))
        label.pack(pady=50)
        
        def close_test():
            root.destroy()
            print("✅ GUI 建立測試通過")
        
        button = tk.Button(root, text="關閉測試", command=close_test)
        button.pack(pady=20)
        
        # 自動關閉測試視窗
        root.after(2000, close_test)  # 2秒後自動關閉
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ GUI 建立失敗: {e}")
        return False

def test_image_processing():
    """測試圖像處理功能"""
    print("\n測試圖像處理...")
    
    try:
        # 建立測試圖像
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        test_image[:] = (100, 150, 200)  # 填充顏色
        
        # 添加一些圖形
        cv2.rectangle(test_image, (100, 100), (200, 200), (255, 0, 0), 2)
        cv2.circle(test_image, (320, 240), 50, (0, 255, 0), 2)
        cv2.putText(test_image, "Test Image", (250, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 測試圖像轉換
        from PIL import Image, ImageTk
        image_rgb = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image_rgb)
        
        # 測試調整大小
        resized = pil_image.resize((320, 240))
        
        print("✅ 圖像處理測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 圖像處理測試失敗: {e}")
        return False

def test_detector_import():
    """測試檢測器導入"""
    print("\n測試檢測器導入...")
    
    try:
        # 檢查原始檢測器檔案是否存在
        if not os.path.exists('gpu_optimized_head_detection.py'):
            print("❌ 找不到 gpu_optimized_head_detection.py")
            return False
        
        # 嘗試導入檢測器類別
        from gpu_optimized_head_detection import GPUOptimizedHeadDetector
        print("✅ 檢測器類別導入成功")
        
        # 注意：這裡不實際初始化檢測器，因為可能需要下載模型
        print("✅ 檢測器導入測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 檢測器導入失敗: {e}")
        return False

def test_gui_import():
    """測試 GUI 模組導入"""
    print("\n測試 GUI 模組導入...")
    
    try:
        # 檢查 GUI 檔案是否存在
        if not os.path.exists('head_detection_gui.py'):
            print("❌ 找不到 head_detection_gui.py")
            return False
        
        # 嘗試導入 GUI 類別（不執行）
        import importlib.util
        spec = importlib.util.spec_from_file_location("head_detection_gui", "head_detection_gui.py")
        gui_module = importlib.util.module_from_spec(spec)
        
        print("✅ GUI 模組導入測試通過")
        return True
        
    except Exception as e:
        print(f"❌ GUI 模組導入失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("=" * 50)
    print("頭部檢測 GUI 系統測試")
    print("=" * 50)
    
    tests = [
        ("套件導入", test_imports),
        ("圖像處理", test_image_processing),
        ("檢測器導入", test_detector_import),
        ("GUI 模組導入", test_gui_import),
        ("GUI 建立", test_gui_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    print("=" * 50)
    
    if passed == total:
        print("🎉 所有測試通過！GUI 系統準備就緒。")
        print("\n可以使用以下命令啟動 GUI：")
        print("python run_gui.py")
        print("或")
        print("python head_detection_gui.py")
        return 0
    else:
        print("⚠️  部分測試失敗，請檢查環境配置。")
        return 1

if __name__ == "__main__":
    exit(main())
