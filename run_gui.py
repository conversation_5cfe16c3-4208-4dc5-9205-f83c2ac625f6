#!/usr/bin/env python3
"""
頭部檢測 GUI 啟動腳本
檢查環境並啟動圖形化介面
"""

import sys
import os
import subprocess
import importlib.util

def check_package(package_name):
    """檢查套件是否已安裝"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_package(package_name):
    """安裝套件"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def check_and_install_requirements():
    """檢查並安裝必要套件"""
    required_packages = {
        'torch': 'torch',
        'torchvision': 'torchvision', 
        'cv2': 'opencv-python',
        'PIL': 'Pillow',
        'matplotlib': 'matplotlib',
        'numpy': 'numpy',
        'ultralytics': 'ultralytics'
    }
    
    missing_packages = []
    
    print("檢查必要套件...")
    for import_name, package_name in required_packages.items():
        if not check_package(import_name):
            missing_packages.append(package_name)
            print(f"❌ {package_name} 未安裝")
        else:
            print(f"✅ {package_name} 已安裝")
    
    if missing_packages:
        print(f"\n發現 {len(missing_packages)} 個缺少的套件")
        response = input("是否要自動安裝缺少的套件？(y/n): ")
        
        if response.lower() in ['y', 'yes', '是']:
            print("\n開始安裝套件...")
            for package in missing_packages:
                print(f"正在安裝 {package}...")
                if install_package(package):
                    print(f"✅ {package} 安裝成功")
                else:
                    print(f"❌ {package} 安裝失敗")
                    return False
        else:
            print("請手動安裝缺少的套件後再執行程式")
            return False
    
    return True

def check_gpu_support():
    """檢查 GPU 支援"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ GPU 支援可用: {gpu_count} 個 GPU")
            print(f"   主要 GPU: {gpu_name}")
            return True
        else:
            print("⚠️  GPU 不可用，將使用 CPU 模式")
            return False
    except ImportError:
        print("⚠️  無法檢查 GPU 狀態")
        return False

def main():
    """主程式"""
    print("=" * 50)
    print("頭部檢測系統 GUI 啟動器")
    print("=" * 50)
    
    # 檢查 Python 版本
    if sys.version_info < (3, 7):
        print("❌ 需要 Python 3.7 或更高版本")
        print(f"   當前版本: {sys.version}")
        return 1
    else:
        print(f"✅ Python 版本: {sys.version.split()[0]}")
    
    # 檢查並安裝套件
    if not check_and_install_requirements():
        print("\n❌ 套件安裝失敗，無法啟動程式")
        return 1
    
    # 檢查 GPU 支援
    check_gpu_support()
    
    # 檢查核心檔案是否存在
    required_files = ['head_detection_gui.py', 'gpu_optimized_head_detection.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 找不到必要檔案: {file}")
            return 1
        else:
            print(f"✅ 找到檔案: {file}")
    
    print("\n" + "=" * 50)
    print("環境檢查完成，啟動 GUI...")
    print("=" * 50)
    
    # 啟動 GUI
    try:
        from head_detection_gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ GUI 啟動失敗: {e}")
        print("\n請檢查錯誤訊息並確保所有依賴套件都已正確安裝")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
