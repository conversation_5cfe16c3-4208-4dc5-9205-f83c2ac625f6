# RTSP 串流功能說明

## 🎯 功能概述

GUI 現已完整整合原始程式的 RTSP 串流檢測功能，提供即時視頻串流的頭部檢測能力。

## 📋 新增功能清單

### 1. RTSP 串流連接 ✅
- **串流輸入**：支援標準 RTSP 協議串流
- **連接管理**：連接/斷開控制
- **狀態監控**：即時連接狀態顯示
- **錯誤處理**：完整的連接錯誤處理機制

### 2. 即時檢測處理 ✅
- **即時檢測**：串流畫面即時頭部檢測
- **效能優化**：每5幀檢測一次提高效率
- **多線程處理**：不影響 GUI 響應性
- **結果顯示**：即時檢測結果和統計更新

### 3. ROI 區域設定 ✅
- **多區域支援**：可設定多個感興趣區域
- **互動繪製**：滑鼠點擊繪製 ROI 區域
- **即時預覽**：ROI 區域即時顯示
- **檔案管理**：ROI 設定載入和儲存

### 4. 統計數據整合 ✅
- **即時統計**：串流檢測數據即時統計
- **圖表更新**：檢測趨勢圖表即時更新
- **區域分析**：多區域檢測結果分析
- **數據匯出**：完整的統計數據匯出

## 🖥️ 介面更新

### 控制面板新增項目
```
輸入來源
├── 選擇圖片
├── 選擇影片
├── RTSP 串流地址 [輸入框]
├── [連接 RTSP] [斷開連接]
└── 

ROI 區域設定
├── [開始繪製 ROI] [清除 ROI]
├── [載入 ROI] [儲存 ROI]
└── ROI 狀態: X 個區域
```

### 檢測結果顯示
- **原始畫面**：RTSP 串流原始畫面
- **檢測結果**：帶檢測框和 ROI 區域的結果畫面
- **即時更新**：串流畫面即時更新顯示

## 🔧 使用方法

### RTSP 串流連接
1. **輸入 RTSP URL**
   ```
   格式：rtsp://用戶名:密碼@IP地址:端口/路徑
   範例：rtsp://admin:123456@*************:554/stream
   ```

2. **連接串流**
   - 點擊「連接 RTSP」按鈕
   - 等待連接成功提示
   - 開始即時檢測

3. **斷開連接**
   - 點擊「斷開連接」按鈕
   - 或關閉程式自動斷開

### ROI 區域設定
1. **開始繪製**
   - 點擊「開始繪製 ROI」進入編輯模式
   - 在檢測結果畫面上點擊滑鼠左鍵添加點
   - 至少需要3個點形成區域

2. **完成區域**
   - 點擊滑鼠右鍵完成當前區域
   - 系統自動命名區域（Region_1, Region_2...）
   - 可繼續繪製更多區域

3. **管理區域**
   - 點擊「清除 ROI」刪除所有區域
   - 點擊「儲存 ROI」保存設定到檔案
   - 點擊「載入 ROI」從檔案載入設定

## 📊 統計功能增強

### 即時統計顯示
- **總檢測數量**：所有區域累計檢測數量
- **區域統計**：各 ROI 區域獨立統計
- **檢測歷史**：完整的檢測歷史記錄
- **效能監控**：FPS 和 GPU 記憶體使用

### 圖表分析
- **趨勢圖**：檢測數量變化趨勢（每20幀更新）
- **分佈圖**：各區域檢測結果分佈
- **即時更新**：串流過程中圖表即時更新

## 🛠️ 技術實現

### 多線程架構
```python
主線程 (GUI)
├── 事件處理
├── 介面更新
└── 結果顯示

RTSP 工作線程
├── 串流連接
├── 幀讀取
├── 檢測處理
└── 結果傳遞

結果處理線程
├── 結果佇列處理
├── 統計更新
└── 圖表更新
```

### 核心組件整合
- **GPUOptimizedRTSPProcessor**：原始 RTSP 處理器
- **多區域 ROI 支援**：完整的多區域檢測功能
- **統計數據收集**：即時統計和歷史記錄
- **視覺化顯示**：ROI 區域和檢測結果顯示

## 🧪 測試工具

### RTSP 測試腳本
```bash
python test_rtsp_gui.py
```

功能：
- RTSP 連接測試
- 串流品質評估
- 檢測器整合驗證
- GUI 啟動輔助

### 完整測試
```bash
python test_gui.py
```

包含：
- 所有原有功能測試
- RTSP 功能測試
- 整合測試驗證

## 📝 設定檔案

### ROI 設定檔案格式
```json
{
  "regions": [
    {
      "id": 0,
      "name": "Region_1",
      "points": [[x1, y1], [x2, y2], [x3, y3], ...],
      "closed": true,
      "created_at": "2024-01-01T12:00:00"
    }
  ],
  "created_at": "2024-01-01T12:00:00",
  "total_regions": 1
}
```

### 統計報告格式
- 包含 RTSP 串流資訊
- 多區域檢測統計
- 效能監控數據
- 完整檢測歷史

## 🔍 故障排除

### RTSP 連接問題
- **無法連接**：檢查 URL 格式和網路連接
- **認證失敗**：確認用戶名和密碼正確
- **串流中斷**：檢查網路穩定性
- **畫面延遲**：調整檢測間隔參數

### ROI 設定問題
- **無法繪製**：確認已進入 ROI 編輯模式
- **區域無效**：確保至少有3個點
- **座標錯誤**：檢查畫面縮放和座標轉換
- **檔案錯誤**：確認 JSON 格式正確

## 🎉 功能完成度

✅ **RTSP 串流連接** - 100% 完成
✅ **即時檢測處理** - 100% 完成  
✅ **多區域 ROI 設定** - 100% 完成
✅ **統計數據整合** - 100% 完成
✅ **介面整合** - 100% 完成
✅ **測試驗證** - 100% 完成

## 📚 相關文件

- `README_GUI.md` - 完整技術文件
- `使用指南.md` - 詳細使用說明
- `GUI_專案總結.md` - 專案總結報告
- `test_rtsp_gui.py` - RTSP 測試工具

現在 GUI 系統已經完整整合了原始程式的所有核心功能，包括 RTSP 串流檢測、多區域 ROI 設定、即時統計分析等，提供了一個功能完整、使用者友善的頭部檢測解決方案。
