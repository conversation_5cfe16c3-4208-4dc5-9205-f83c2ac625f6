# 頭部檢測 GUI 系統使用指南

## 🚀 快速開始

### 1. 環境檢查
執行測試腳本確認環境：
```bash
python test_gui.py
```

### 2. 啟動 GUI
推薦使用啟動腳本：
```bash
python run_gui.py
```

或直接啟動：
```bash
python head_detection_gui.py
```

### 3. 測試檢測功能
使用提供的測試圖片：
- `test_images/simple_test.jpg` - 3個人物的簡單場景
- `test_images/complex_test.jpg` - 12個人物的複雜場景
- `test_images/blank_test.jpg` - 無人物的空白場景

## 📋 操作步驟

### 基本檢測流程

1. **載入檔案**
   - 點擊「選擇圖片」載入圖片檔案
   - 或點擊「選擇影片」載入影片檔案
   - 原始圖片會顯示在「原始圖片」標籤頁

2. **調整參數**
   - **信心度閾值**：拖動滑桿調整（0.1-0.9）
     - 較低值：檢測更多目標，可能有誤檢
     - 較高值：檢測更準確，可能遺漏目標
   - **模型類型**：選擇檢測模型
     - `person`：通用人體檢測模型
     - `custom`：專用頭部檢測模型（需要模型檔案）
   - **運算設備**：選擇運算方式
     - `cuda`：GPU 加速（需要 NVIDIA GPU）
     - `cpu`：CPU 運算（較慢但相容性好）

3. **執行檢測**
   - 點擊「開始檢測」執行檢測
   - 進度條會顯示處理狀態
   - 檢測結果會顯示在「檢測結果」標籤頁
   - 右側統計面板會即時更新

4. **查看結果**
   - **檢測圖片**：帶有檢測框和標籤的結果圖片
   - **統計資訊**：檢測數量、平均值、最大值等
   - **趨勢圖表**：檢測數量變化趨勢
   - **分佈圖表**：區域檢測結果分佈

5. **儲存結果**
   - 點擊「儲存結果」匯出 JSON 格式統計數據
   - 點擊「匯出統計」匯出 PNG/PDF 格式圖表

### 影片檢測特殊說明

- 影片會逐幀檢測（每5幀檢測一次以提高效率）
- 檢測過程中可隨時點擊「停止檢測」中斷
- 統計圖表會即時更新顯示檢測趨勢
- 處理大型影片檔案需要較長時間

## 🎛️ 介面說明

### 主視窗佈局
```
┌─────────────────────────────────────────────────────────┐
│ 檔案  檢測  說明                                        │
├─────────────┬─────────────────────────┬─────────────────┤
│  控制面板   │      顯示區域           │   統計面板      │
│             │  ┌─原始圖片─┐          │                 │
│ 檔案選擇    │  └─檢測結果─┘          │ 即時統計        │
│ 檢測參數    │                         │                 │
│ 顯示選項    │                         │ 統計圖表        │
│ 操作控制    │                         │ • 趨勢圖        │
│             │                         │ • 分佈圖        │
├─────────────┴─────────────────────────┴─────────────────┤
│ 狀態：就緒                      檢測資訊：檢測到 X 個頭部 │
└─────────────────────────────────────────────────────────┘
```

### 選單功能
- **檔案選單**
  - 開啟圖片/影片
  - 儲存結果
  - 匯出統計
  - 退出程式

- **檢測選單**
  - 開始/停止檢測
  - 重設統計數據

- **說明選單**
  - 使用說明
  - 關於程式

## 📊 統計功能

### 即時統計顯示
- **總檢測數量**：累計檢測到的頭部總數
- **檢測次數**：執行檢測的次數
- **近期平均**：最近10次檢測的平均數量
- **近期最大**：最近10次檢測的最大數量
- **區域統計**：各區域的詳細統計（如果有多區域）

### 統計圖表
1. **檢測數量趨勢圖**
   - 顯示最近20次檢測的數量變化
   - 包含平均線參考
   - 適合觀察檢測結果的變化趨勢

2. **區域分佈圓餅圖**
   - 顯示各區域檢測結果的比例
   - 不同顏色代表不同區域
   - 適合分析區域間的差異

### 數據匯出
- **JSON 格式**：包含完整的檢測歷史和統計數據
- **圖表格式**：PNG 或 PDF 格式的統計圖表
- **包含資訊**：
  - 會話資訊（開始時間、參數設定等）
  - 檢測統計（總數、平均值、最大值等）
  - 檢測歷史（每次檢測的詳細記錄）

## ⚙️ 參數調整建議

### 信心度閾值設定
- **0.1-0.3**：低閾值，檢測更多目標，適合確保不遺漏
- **0.4-0.6**：中等閾值，平衡準確性和召回率
- **0.7-0.9**：高閾值，只檢測高信心目標，減少誤檢

### 模型選擇建議
- **person 模型**：
  - 適用於一般場景
  - 檢測整個人體，然後提取頭部區域
  - 相容性好，無需額外模型檔案

- **custom 模型**：
  - 專門訓練的頭部檢測模型
  - 檢測精度更高
  - 需要 `yolov5s-head.pt` 模型檔案

### 設備選擇建議
- **CUDA (GPU)**：
  - 處理速度快，適合大量檢測
  - 需要 NVIDIA GPU 和 CUDA 支援
  - 推薦用於影片檢測

- **CPU**：
  - 相容性好，所有電腦都支援
  - 處理速度較慢
  - 適合偶爾使用或小量檢測

## 🔧 故障排除

### 常見問題

**Q: 程式無法啟動**
- 檢查 Python 版本（需要 3.7+）
- 執行 `python test_gui.py` 檢查環境
- 確認所有必要套件已安裝

**Q: 檢測器初始化失敗**
- 檢查網路連接（首次使用需下載模型）
- 確認 PyTorch 和相關套件版本相容
- 嘗試切換到 CPU 模式

**Q: GPU 模式不可用**
- 確認已安裝 NVIDIA GPU 驅動
- 檢查 CUDA 版本與 PyTorch 版本相容性
- 執行 `python -c "import torch; print(torch.cuda.is_available())"` 檢查

**Q: 檢測結果不準確**
- 調整信心度閾值
- 嘗試不同的模型類型
- 確認圖片品質和解析度

**Q: 處理速度很慢**
- 使用 GPU 模式（如果可用）
- 降低圖片解析度
- 關閉其他佔用資源的程式

### 錯誤訊息解釋
- `檢測器初始化失敗`：模型載入問題，檢查網路和套件
- `無法載入圖片/影片`：檔案格式或路徑問題
- `CUDA 不可用`：GPU 驅動或 CUDA 安裝問題
- `記憶體不足`：圖片太大或系統記憶體不足

## 💡 使用技巧

1. **首次使用**：建議先用測試圖片熟悉介面
2. **參數調整**：從預設值開始，根據結果逐步調整
3. **批次處理**：影片檢測可以處理大量幀，適合批次分析
4. **結果分析**：善用統計圖表分析檢測趨勢和模式
5. **數據備份**：定期匯出統計數據以備份重要結果

## 📞 技術支援

如遇到問題：
1. 查看程式日誌輸出
2. 執行測試腳本診斷環境
3. 檢查本指南的故障排除章節
4. 確認系統需求和相依套件版本
